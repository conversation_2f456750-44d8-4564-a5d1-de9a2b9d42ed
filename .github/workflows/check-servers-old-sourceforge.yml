# ----------------------------------------------------------------------------
# GitHub Actions workflow to regularly check SourceForge servers,
# do they redirect to new servers.
#
# This is a separate workflow from check-servers.yml because
# it often fails in 2024, and there's nothing we can do about it, it seems
# -- SourceForge is not reliable. So we can easily ignore occasional
# notifications when it fails.
# ----------------------------------------------------------------------------

name: Check Old SourceForge Servers Online

on:
  schedule:
# Changed the frequency from hourly to daily,
# see https://docs.github.com/en/actions/writing-workflows/choosing-when-your-workflow-runs/events-that-trigger-workflows .
# Because redirects on SourceForge seems broken now, despite correct files in place.
# To be investigated later, or maybe SF will fix it.
#    - cron: '33 * * * *'
    - cron: '33 5 * * *'
  workflow_dispatch:

defaults:
  run:
    shell: bash

jobs:
  check_servers:
    name: Check Old SourceForge Servers
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Check Old SourceForge Online
        run: ./scripts/check_online_sourceforge.sh
