# ----------------------------------------------------------------------------
# GitHub Actions workflow to regularly check servers.
# ----------------------------------------------------------------------------

name: Check Servers Online

on:
  schedule:
    - cron: '23 * * * *'
  workflow_dispatch:

defaults:
  run:
    shell: bash

jobs:
  check_servers:
    name: Check Servers
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Check Online
        run: ./scripts/check_online.sh
