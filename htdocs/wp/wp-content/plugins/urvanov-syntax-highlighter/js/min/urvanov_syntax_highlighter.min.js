var jQueryUrvanovSyntaxHighlighter=jQuery;(function(a){UrvanovSyntaxHighlighterUtil=new function(){var c=this;var b=null;c.init=function(){b=UrvanovSyntaxHighlighterSyntaxSettings;c.initGET()};c.addPrefixToID=function(d){return d.replace(/^([#.])?(.*)$/,"$1"+b.prefix+"$2")};c.removePrefixFromID=function(e){var d=new RegExp("^[#.]?"+b.prefix,"i");return e.replace(d,"")};c.cssElem=function(d){return a(c.addPrefixToID(d))};c.setDefault=function(e,f){return(typeof e=="undefined")?f:e};c.setMax=function(e,d){return e<=d?e:d};c.setMin=function(d,e){return d>=e?d:e};c.setRange=function(e,f,d){return c.setMax(c.setMin(e,f),d)};c.getExt=function(e){if(e.indexOf(".")==-1){return undefined}var d=e.split(".");if(d.length){d=d[d.length-1]}else{d=""}return d};c.initGET=function(){window.currentURL=window.location.protocol+"//"+window.location.host+window.location.pathname;window.currentDir=window.currentURL.substring(0,window.currentURL.lastIndexOf("/"));function d(e){e=e.split("+").join(" ");var h={},g,f=/[?&]?([^=]+)=([^&]*)/g;while(g=f.exec(e)){h[decodeURIComponent(g[1])]=decodeURIComponent(g[2])}return h}window.GET=d(document.location.search)};c.getAJAX=function(d,e){d.version=b.version;a.get(b.ajaxurl,d,e)};c.htmlToElements=function(d){return a.parseHTML(d,document,true)};c.postAJAX=function(d,e){d.version=b.version;a.post(b.ajaxurl,d,e)};c.reload=function(){var d="?";for(var e in window.GET){d+=e+"="+window.GET[e]+"&"}window.location=window.currentURL+d};c.escape=function(d){if(typeof encodeURIComponent=="function"){return encodeURIComponent(d)}else{if(typeof escape!="function"){return escape(d)}else{return d}}};c.log=function(d){if(typeof console!="undefined"&&b&&b.debug){console.log(d)}};c.decode_html=function(d){return String(d).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};c.encode_html=function(d){return String(d).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")};c.getReadableColor=function(g,f){f=a.extend({amount:0.5,xMulti:1,yMulti:1.5,normalizeHue:[20,180],normalizeHueXMulti:1/2.5,normalizeHueYMulti:1},f);var d=tinycolor(g);var e=d.toHsv();var i={x:e.s,y:1-e.v};i.x*=f.xMulti;i.y*=f.yMulti;if(f.normalizeHue&&e.h>f.normalizeHue[0]&&e.h<f.normalizeHue[1]){i.x*=f.normalizeHueXMulti;i.y*=f.normalizeHueYMulti}var h=Math.sqrt(Math.pow(i.x,2)+Math.pow(i.y,2));if(h<f.amount){e.v=0}else{e.v=1}e.s=0;return tinycolor(e).toHexString()};c.removeChars=function(e,f){var d=new RegExp("["+e+"]","gmi");return f.replace(d,"")}};a(document).ready(function(){UrvanovSyntaxHighlighterUtil.init()});a.fn.bindFirst=function(c,e){this.bind(c,e);var b=this.data("events")[c.split(".")[0]];var d=b.pop();b.splice(0,0,d)};a.keys=function(d){var c=[];for(var b in d){c.push(b)}return c};RegExp.prototype.execAll=function(c){var f=[];var b=null;while((b=this.exec(c))!=null){var e=[];for(var d in b){if(parseInt(d)==d){e.push(b[d])}}f.push(e)}return f};RegExp.prototype.escape=function(b){return b.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")};String.prototype.sliceReplace=function(d,b,c){return this.substring(0,d)+c+this.substring(b)};String.prototype.escape=function(){var b={"&":"&amp;","<":"&lt;",">":"&gt;"};return this.replace(/[&<>]/g,function(c){return b[c]||c})};String.prototype.linkify=function(b){b=typeof b!="undefined"?b:"";return this.replace(/(http(s)?:\/\/(\S)+)/gmi,'<a href="$1" target="'+b+'">$1</a>')};String.prototype.toTitleCase=function(){var b=this.split(/\s+/);var c="";a.each(b,function(e,d){if(d!=""){c+=d.slice(0,1).toUpperCase()+d.slice(1,d.length);if(e!=b.length-1&&b[e+1]!=""){c+=" "}}});return c}})(jQueryUrvanovSyntaxHighlighter);jqueryPopup=Object();jqueryPopup.defaultSettings={centerBrowser:0,centerScreen:0,height:500,left:0,location:0,menubar:0,resizable:0,scrollbars:0,status:0,width:500,windowName:null,windowURL:null,top:0,toolbar:0,data:null,event:"click"};(function(a){popupWindow=function(d,c,f,b){f=typeof f!=="undefined"?f:null;b=typeof b!=="undefined"?b:null;if(typeof d=="string"){d=jQuery(d)}if(!(d instanceof jQuery)){return false}var e=jQuery.extend({},jqueryPopup.defaultSettings,c||{});d.handler=jQuery(d).bind(e.event,function(){if(f){f()}var g="height="+e.height+",width="+e.width+",toolbar="+e.toolbar+",scrollbars="+e.scrollbars+",status="+e.status+",resizable="+e.resizable+",location="+e.location+",menuBar="+e.menubar;e.windowName=e.windowName||jQuery(this).attr("name");var h=jQuery(this).attr("href");if(!e.windowURL&&!(h=="#")&&!(h=="")){e.windowURL=jQuery(this).attr("href")}var i,j;var k=null;if(e.centerBrowser){if(typeof window.screenY=="undefined"){i=(window.screenTop-120)+((((document.documentElement.clientHeight+120)/2)-(e.height/2)));j=window.screenLeft+((((document.body.offsetWidth+20)/2)-(e.width/2)))}else{i=window.screenY+(((window.outerHeight/2)-(e.height/2)));j=window.screenX+(((window.outerWidth/2)-(e.width/2)))}k=window.open(e.windowURL,e.windowName,g+",left="+j+",top="+i)}else{if(e.centerScreen){i=(screen.height-e.height)/2;j=(screen.width-e.width)/2;k=window.open(e.windowURL,e.windowName,g+",left="+j+",top="+i)}else{k=window.open(e.windowURL,e.windowName,g+",left="+e.left+",top="+e.top)}}if(k!=null){k.focus();if(e.data){k.document.write(e.data)}}if(b){b()}});return e};popdownWindow=function(b,c){if(typeof c=="undefined"){c="click"}b=jQuery(b);if(!(b instanceof jQuery)){return false}b.unbind(c,b.handler)}})(jQueryUrvanovSyntaxHighlighter);(function(g){g.fn.exists=function(){return this.length!==0};g.fn.style=function(B,E,A){var D=this.get(0);if(typeof D=="undefined"){return}var C=D.style;if(typeof B!="undefined"){if(typeof E!="undefined"){A=typeof A!="undefined"?A:"";if(typeof C.setProperty!="undefined"){C.setProperty(B,E,A)}else{C[B]=E}}else{return C[B]}}else{return C}};var e="crayon-pressed";var a="";var j="div.urvanov-syntax-highlighter-syntax";var y=".crayon-toolbar";var k=".crayon-info";var i=".urvanov-syntax-highlighter-plain";var u=".urvanov-syntax-highlighter-main";var x=".crayon-table";var t=".urvanov-syntax-highlighter-loading";var p=".urvanov-syntax-highlighter-code";var d=".crayon-title";var w=".crayon-tools";var h=".crayon-nums";var o=".crayon-num";var b=".crayon-line";var c="urvanov-syntax-highlighter-wrapped";var v=".urvanov-syntax-highlighter-nums-content";var n=".urvanov-syntax-highlighter-nums-button";var s=".urvanov-syntax-highlighter-wrap-button";var r=".urvanov-syntax-highlighter-expand-button";var q="urvanov-syntax-highlighter-expanded urvanov-syntax-highlighter-toolbar-visible";var f="urvanov-syntax-highlighter-placeholder";var z=".urvanov-syntax-highlighter-popup-button";var l=".urvanov-syntax-highlighter-copy-button";var m=".urvanov-syntax-highlighter-plain-button";UrvanovSyntaxHighlighterSyntax=new function(){var J=this;var B=new Object();var ag;var H;var G=0;var Z;J.init=function(){if(typeof B=="undefined"){B=new Object()}ag=UrvanovSyntaxHighlighterSyntaxSettings;H=UrvanovSyntaxHighlighterSyntaxStrings;g(j).each(function(){J.process(this)})};J.process=function(aD,aE){aD=g(aD);var ar=aD.attr("id");if(ar=="urvanov-syntax-highlighter-"){ar+=X()}aD.attr("id",ar);UrvanovSyntaxHighlighterUtil.log(ar);if(typeof aE=="undefined"){aE=false}if(!aE&&!aa(ar)){return}var au=aD.find(y);var aC=aD.find(k);var ap=aD.find(i);var aq=aD.find(u);var aB=aD.find(x);var aj=aD.find(p);var aG=aD.find(d);var aA=aD.find(w);var ay=aD.find(h);var av=aD.find(v);var az=aD.find(n);var am=aD.find(s);var ao=aD.find(r);var aF=aD.find(z);var at=aD.find(l);var al=aD.find(m);B[ar]=aD;B[ar].toolbar=au;B[ar].plain=ap;B[ar].info=aC;B[ar].main=aq;B[ar].table=aB;B[ar].code=aj;B[ar].title=aG;B[ar].tools=aA;B[ar].nums=ay;B[ar].nums_content=av;B[ar].numsButton=az;B[ar].wrapButton=am;B[ar].expandButton=ao;B[ar].popup_button=aF;B[ar].copy_button=at;B[ar].plainButton=al;B[ar].numsVisible=true;B[ar].wrapped=false;B[ar].plainVisible=false;B[ar].toolbar_delay=0;B[ar].time=1;g(i).css("z-index",0);var aw=aq.style();B[ar].mainStyle={height:aw&&aw.height||"","max-height":aw&&aw.maxHeight||"","min-height":aw&&aw.minHeight||"",width:aw&&aw.width||"","max-width":aw&&aw.maxWidth||"","min-width":aw&&aw.minWidth||""};B[ar].mainHeightAuto=B[ar].mainStyle.height==""&&B[ar].mainStyle["max-height"]=="";var ak;var ax=0;B[ar].loading=true;B[ar].scrollBlockFix=false;az.click(function(){UrvanovSyntaxHighlighterSyntax.toggleNums(ar)});am.click(function(){UrvanovSyntaxHighlighterSyntax.toggleWrap(ar)});ao.click(function(){UrvanovSyntaxHighlighterSyntax.toggleExpand(ar)});al.click(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar)});at.click(function(){UrvanovSyntaxHighlighterSyntax.copyPlain(ar)});A(ar);var an=function(){if(ay.filter('[data-settings~="hide"]').length!=0){av.ready(function(){UrvanovSyntaxHighlighterUtil.log("function"+ar);UrvanovSyntaxHighlighterSyntax.toggleNums(ar,true,true)})}else{ac(ar)}if(typeof B[ar].expanded=="undefined"){if(Math.abs(B[ar].main.outerWidth()-B[ar].table.outerWidth())<10){B[ar].expandButton.hide()}else{B[ar].expandButton.show()}}if(ax==5){clearInterval(ak);B[ar].loading=false}ax++};ak=setInterval(an,300);C(ar);g(o,B[ar]).each(function(){var aJ=g(this).attr("data-line");var aI=g("#"+aJ);var aH=aI.style("height");if(aH){aI.attr("data-height",aH)}});aq.css("position","relative");aq.css("z-index",1);Z=(aD.filter('[data-settings~="touchscreen"]').length!=0);if(!Z){aq.click(function(){I(ar,"",false)});ap.click(function(){I(ar,"",false)});aC.click(function(){I(ar,"",false)})}if(aD.filter('[data-settings~="no-popup"]').length==0){B[ar].popup_settings=popupWindow(aF,{height:screen.height-200,width:screen.width-100,top:75,left:50,scrollbars:1,windowURL:"",data:""},function(){F(ar)},function(){})}ap.css("opacity",0);B[ar].toolbarVisible=true;B[ar].hasOneLine=aB.outerHeight()<au.outerHeight()*2;B[ar].toolbarMouseover=false;if(au.filter('[data-settings~="mouseover"]').length!=0&&!Z){B[ar].toolbarMouseover=true;B[ar].toolbarVisible=false;au.css("margin-top","-"+au.outerHeight()+"px");au.hide();if(au.filter('[data-settings~="overlay"]').length!=0&&!B[ar].hasOneLine){au.css("position","absolute");au.css("z-index",2);if(au.filter('[data-settings~="hide"]').length!=0){aq.click(function(){T(ar,undefined,undefined,0)});ap.click(function(){T(ar,false,undefined,0)})}}else{au.css("z-index",4)}if(au.filter('[data-settings~="delay"]').length!=0){B[ar].toolbar_delay=500}aD.mouseenter(function(){T(ar,true)}).mouseleave(function(){T(ar,false)})}else{if(Z){au.show()}}if(aD.filter('[data-settings~="minimize"]').length==0){J.minimize(ar)}if(ap.length!=0&&!Z){if(ap.filter('[data-settings~="dblclick"]').length!=0){aq.dblclick(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar)})}else{if(ap.filter('[data-settings~="click"]').length!=0){aq.click(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar)})}else{if(ap.filter('[data-settings~="mouseover"]').length!=0){aD.mouseenter(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar,true)}).mouseleave(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar,false)});az.hide()}}}if(ap.filter('[data-settings~="show-plain-default"]').length!=0){UrvanovSyntaxHighlighterSyntax.togglePlain(ar,true)}}var ai=aD.filter('[data-settings~="expand"]').length!=0;if(!Z&&aD.filter('[data-settings~="scroll-mouseover"]').length!=0){aq.css("overflow","hidden");ap.css("overflow","hidden");aD.mouseenter(function(){N(ar,true,ai)}).mouseleave(function(){N(ar,false,ai)})}if(ai){aD.mouseenter(function(){D(ar,true)}).mouseleave(function(){D(ar,false)})}if(aD.filter('[data-settings~="disable-anim"]').length!=0){B[ar].time=0}if(aD.filter('[data-settings~="wrap"]').length!=0){B[ar].wrapped=true}B[ar].mac=aD.hasClass("urvanov-syntax-highlighter-os-mac");ac(ar);ab(ar);Y(ar)};var aa=function(ai){UrvanovSyntaxHighlighterUtil.log(B);if(typeof B[ai]=="undefined"){B[ai]=g("#"+ai);UrvanovSyntaxHighlighterUtil.log("make "+ai);return true}UrvanovSyntaxHighlighterUtil.log("no make "+ai);return false};var X=function(){return G++};var F=function(ai){if(typeof B[ai]=="undefined"){return aa(ai)}var aj=B[ai].popup_settings;if(aj&&aj.data){return}var al=B[ai].clone(true);al.removeClass("urvanov-syntax-highlighter-wrapped");if(B[ai].wrapped){g(o,al).each(function(){var ao=g(this).attr("data-line");var an=g("#"+ao);var am=an.attr("data-height");am=am?am:"";if(typeof am!="undefined"){an.css("height",am);g(this).css("height",am)}})}al.find(u).css("height","");var ak="";if(B[ai].plainVisible){ak=al.find(i)}else{ak=al.find(u)}aj.data=J.getAllCSS()+'<body class="urvanov-syntax-highlighter-popup-window" style="padding:0; margin:0;"><div class="'+al.attr("class")+' urvanov-syntax-highlighter-popup">'+J.removeCssInline(J.getHtmlString(ak))+"</div></body>"};J.minimize=function(al){var ak=g('<div class="urvanov-syntax-highlighter-minimize urvanov-syntax-highlighter-button"><div>');B[al].tools.append(ak);B[al].origTitle=B[al].title.html();if(!B[al].origTitle){B[al].title.html(H.minimize)}var aj="urvanov-syntax-highlighter-minimized";var ai=function(){B[al].toolbarPreventHide=false;ak.remove();B[al].removeClass(aj);B[al].title.html(B[al].origTitle);var am=B[al].toolbar;if(am.filter('[data-settings~="never-show"]').length!=0){am.remove()}};B[al].toolbar.click(ai);ak.click(ai);B[al].addClass(aj);B[al].toolbarPreventHide=true;T(al,undefined,undefined,0)};J.getHtmlString=function(ai){return g("<div>").append(ai.clone()).remove().html()};J.removeCssInline=function(ak){var aj=/style\s*=\s*"([^"]+)"/gmi;var ai=null;while((ai=aj.exec(ak))!=null){var al=ai[1];al=al.replace(/\b(?:width|height)\s*:[^;]+;/gmi,"");ak=ak.sliceReplace(ai.index,ai.index+ai[0].length,'style="'+al+'"')}return ak};J.getAllCSS=function(){var ak="";var aj=g('link[rel="stylesheet"]');var ai=[];if(aj.length==1){ai=aj}else{ai=aj.filter('[href*="urvanov-syntax-highlighter"], [href*="min/"]')}ai.each(function(){var al=J.getHtmlString(g(this));ak+=al});return ak};J.copyPlain=function(ak,al){if(typeof B[ak]=="undefined"){return aa(ak)}var aj=B[ak].plain;var ai=aj[0].value;navigator.clipboard.writeText(ai).then(function(am){return I(ak,H.copy)});return false};var I=function(aj,al,ai){if(typeof B[aj]=="undefined"){return aa(aj)}var ak=B[aj].info;if(typeof al=="undefined"){al=""}if(typeof ai=="undefined"){ai=true}if(M(ak)&&ai){ak.html("<div>"+al+"</div>");ak.css("margin-top",-ak.outerHeight());ak.show();R(aj,ak,true);setTimeout(function(){R(aj,ak,false)},5000)}if(!ai){R(aj,ak,false)}};var A=function(ai){if(window.devicePixelRatio>1){var aj=g(".urvanov-syntax-highlighter-button-icon",B[ai].toolbar);aj.each(function(){var al=g(this).css("background-image");var ak=al.replace(/\.(?=[^\.]+$)/g,"@2x.");g(this).css("background-size","48px 128px");g(this).css("background-image",ak)})}};var M=function(ai){var aj="-"+ai.outerHeight()+"px";if(ai.css("margin-top")==aj||ai.css("display")=="none"){return true}else{return false}};var R=function(al,ak,aj,an,am,ap){var ai=function(){if(ap){ap(al,ak)}};var ao="-"+ak.outerHeight()+"px";if(typeof aj=="undefined"){if(M(ak)){aj=true}else{aj=false}}if(typeof an=="undefined"){an=100}if(an==false){an=false}if(typeof am=="undefined"){am=0}ak.stop(true);if(aj==true){ak.show();ak.animate({marginTop:0},ah(an,al),ai)}else{if(aj==false){if(ak.css("margin-top")=="0px"&&am){ak.delay(am)}ak.animate({marginTop:ao},ah(an,al),function(){ak.hide();ai()})}}};J.togglePlain=function(al,am,aj){if(typeof B[al]=="undefined"){return aa(al)}var ai=B[al].main;var ak=B[al].plain;if((ai.is(":animated")||ak.is(":animated"))&&typeof am=="undefined"){return}ae(al);var ao,an;if(typeof am!="undefined"){if(am){ao=ai;an=ak}else{ao=ak;an=ai}}else{if(ai.css("z-index")==1){ao=ai;an=ak}else{ao=ak;an=ai}}B[al].plainVisible=(an==ak);B[al].top=ao.scrollTop();B[al].left=ao.scrollLeft();B[al].scrollChanged=false;C(al);ao.stop(true);ao.fadeTo(ah(500,al),0,function(){ao.css("z-index",0)});an.stop(true);an.fadeTo(ah(500,al),1,function(){an.css("z-index",1);if(an==ak){if(aj){ak.select()}else{}}an.scrollTop(B[al].top+1);an.scrollTop(B[al].top);an.scrollLeft(B[al].left+1);an.scrollLeft(B[al].left)});an.scrollTop(B[al].top);an.scrollLeft(B[al].left);ab(al);T(al,false);return false};J.toggleNums=function(am,al,ai){if(typeof B[am]=="undefined"){aa(am);return false}if(B[am].table.is(":animated")){return false}var ao=Math.round(B[am].nums_content.outerWidth()+1);var an="-"+ao+"px";var ak;if(typeof al!="undefined"){ak=false}else{ak=(B[am].table.css("margin-left")==an)}var aj;if(ak){aj="0px";B[am].numsVisible=true}else{B[am].table.css("margin-left","0px");B[am].numsVisible=false;aj=an}if(typeof ai!="undefined"){B[am].table.css("margin-left",aj);ac(am);return false}var ap=(B[am].table.outerWidth()+K(B[am].table.css("margin-left"))>B[am].main.outerWidth());var aq=(B[am].table.outerHeight()>B[am].main.outerHeight());if(!ap&&!aq){B[am].main.css("overflow","hidden")}B[am].table.animate({marginLeft:aj},ah(200,am),function(){if(typeof B[am]!="undefined"){ac(am);if(!ap&&!aq){B[am].main.css("overflow","auto")}}});return false};J.toggleWrap=function(ai){B[ai].wrapped=!B[ai].wrapped;Y(ai)};J.toggleExpand=function(ai){var aj=!UrvanovSyntaxHighlighterUtil.setDefault(B[ai].expanded,false);D(ai,aj)};var Y=function(ai,aj){aj=UrvanovSyntaxHighlighterUtil.setDefault(aj,true);if(B[ai].wrapped){B[ai].addClass(c)}else{B[ai].removeClass(c)}E(ai);if(!B[ai].expanded&&aj){V(ai)}B[ai].wrapTimes=0;clearInterval(B[ai].wrapTimer);B[ai].wrapTimer=setInterval(function(){if(B[ai].is(":visible")){O(ai);B[ai].wrapTimes++;if(B[ai].wrapTimes==5){clearInterval(B[ai].wrapTimer)}}},200)};var ad=function(ai){if(typeof B[ai]=="undefined"){aa(ai);return false}};var K=function(aj){if(typeof aj!="string"){return 0}var ai=aj.replace(/[^-0-9]/g,"");if(ai.length==0){return 0}else{return parseInt(ai)}};var ac=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].numsVisible=="undefined"){return}if(B[ai].numsVisible){B[ai].numsButton.removeClass(a);B[ai].numsButton.addClass(e)}else{B[ai].numsButton.removeClass(e);B[ai].numsButton.addClass(a)}};var E=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].wrapped=="undefined"){return}if(B[ai].wrapped){B[ai].wrapButton.removeClass(a);B[ai].wrapButton.addClass(e)}else{B[ai].wrapButton.removeClass(e);B[ai].wrapButton.addClass(a)}};var W=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].expanded=="undefined"){return}if(B[ai].expanded){B[ai].expandButton.removeClass(a);B[ai].expandButton.addClass(e)}else{B[ai].expandButton.removeClass(e);B[ai].expandButton.addClass(a)}};var ab=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].plainVisible=="undefined"){return}if(B[ai].plainVisible){B[ai].plainButton.removeClass(a);B[ai].plainButton.addClass(e)}else{B[ai].plainButton.removeClass(e);B[ai].plainButton.addClass(a)}};var T=function(aj,ai,al,ak){if(typeof B[aj]=="undefined"){return aa(aj)}else{if(!B[aj].toolbarMouseover){return}else{if(ai==false&&B[aj].toolbarPreventHide){return}else{if(Z){return}}}}var am=B[aj].toolbar;if(typeof ak=="undefined"){ak=B[aj].toolbar_delay}R(aj,am,ai,al,ak,function(){B[aj].toolbarVisible=ai})};var Q=function(ak,ai){var aj=g.extend({},ak);aj.width+=ai.width;aj.height+=ai.height;return aj};var P=function(ak,ai){var aj=g.extend({},ak);aj.width-=ai.width;aj.height-=ai.height;return aj};var U=function(ai){if(typeof B[ai].initialSize=="undefined"){B[ai].toolbarHeight=B[ai].toolbar.outerHeight();B[ai].innerSize={width:B[ai].width(),height:B[ai].height()};B[ai].outerSize={width:B[ai].outerWidth(),height:B[ai].outerHeight()};B[ai].borderSize=P(B[ai].outerSize,B[ai].innerSize);B[ai].initialSize={width:B[ai].main.outerWidth(),height:B[ai].main.outerHeight()};B[ai].initialSize.height+=B[ai].toolbarHeight;B[ai].initialOuterSize=Q(B[ai].initialSize,B[ai].borderSize);B[ai].finalSize={width:B[ai].table.outerWidth(),height:B[ai].table.outerHeight()};B[ai].finalSize.height+=B[ai].toolbarHeight;B[ai].finalSize.width=UrvanovSyntaxHighlighterUtil.setMin(B[ai].finalSize.width,B[ai].initialSize.width);B[ai].finalSize.height=UrvanovSyntaxHighlighterUtil.setMin(B[ai].finalSize.height,B[ai].initialSize.height);B[ai].diffSize=P(B[ai].finalSize,B[ai].initialSize);B[ai].finalOuterSize=Q(B[ai].finalSize,B[ai].borderSize);B[ai].initialSize.height+=B[ai].toolbar.outerHeight()}};var D=function(al,ao){if(typeof B[al]=="undefined"){return aa(al)}if(typeof ao=="undefined"){return}var aj=B[al].main;var aq=B[al].plain;if(ao){if(typeof B[al].expanded=="undefined"){U(al);B[al].expandTime=UrvanovSyntaxHighlighterUtil.setRange(B[al].diffSize.width/3,300,800);B[al].expanded=false;var ap=B[al].finalOuterSize;B[al].placeholder=g("<div></div>");B[al].placeholder.addClass(f);B[al].placeholder.css(ap);B[al].before(B[al].placeholder);B[al].placeholder.css("margin",B[al].css("margin"));g(window).bind("resize",L)}var am={height:"auto","min-height":"none","max-height":"none"};var ai={width:"auto","min-width":"none","max-width":"none"};B[al].outerWidth(B[al].outerWidth());B[al].css({"min-width":"none","max-width":"none"});var an={width:B[al].finalOuterSize.width};if(!B[al].mainHeightAuto&&!B[al].hasOneLine){an.height=B[al].finalOuterSize.height;B[al].outerHeight(B[al].outerHeight())}aj.css(am);aj.css(ai);B[al].stop(true);B[al].animate(an,ah(B[al].expandTime,al),function(){B[al].expanded=true;W(al)});B[al].placeholder.show();g("body").prepend(B[al]);B[al].addClass(q);L()}else{var ar=B[al].initialOuterSize;var ak=B[al].toolbar_delay;if(ar){B[al].stop(true);if(!B[al].expanded){B[al].delay(ak)}var an={width:ar.width};if(!B[al].mainHeightAuto&&!B[al].hasOneLine){an.height=ar.height}B[al].animate(an,ah(B[al].expandTime,al),function(){af(al)})}else{setTimeout(function(){af(al)},ak)}B[al].placeholder.hide();B[al].placeholder.before(B[al]);B[al].css({left:"auto",top:"auto"});B[al].removeClass(q)}ae(al);if(ao){Y(al,false)}};var L=function(){for(uid in B){if(B[uid].hasClass(q)){B[uid].css(B[uid].placeholder.offset())}}};var af=function(ai){B[ai].expanded=false;V(ai);W(ai);if(B[ai].wrapped){Y(ai)}};var N=function(al,aj,am){if(typeof B[al]=="undefined"){return aa(al)}if(typeof aj=="undefined"||am||B[al].expanded){return}var ai=B[al].main;var ak=B[al].plain;if(aj){ai.css("overflow","auto");ak.css("overflow","auto");if(typeof B[al].top!="undefined"){visible=(ai.css("z-index")==1?ai:ak);visible.scrollTop(B[al].top-1);visible.scrollTop(B[al].top);visible.scrollLeft(B[al].left-1);visible.scrollLeft(B[al].left)}}else{visible=(ai.css("z-index")==1?ai:ak);B[al].top=visible.scrollTop();B[al].left=visible.scrollLeft();ai.css("overflow","hidden");ak.css("overflow","hidden")}B[al].scrollChanged=true;C(al)};var C=function(ai){B[ai].table.style("width","100%","important");var aj=setTimeout(function(){B[ai].table.style("width","");clearInterval(aj)},10)};var V=function(ak){var aj=B[ak].main;var ai=B[ak].mainStyle;aj.css(ai);B[ak].css("height","auto");B[ak].css("width",ai.width);B[ak].css("max-width",ai["max-width"]);B[ak].css("min-width",ai["min-width"])};var ae=function(ai){B[ai].plain.outerHeight(B[ai].main.outerHeight())};var O=function(ai){g(o,B[ai]).each(function(){var al=g(this).attr("data-line");var ak=g("#"+al);var aj=null;if(B[ai].wrapped){ak.css("height","");aj=ak.outerHeight();aj=aj?aj:""}else{aj=ak.attr("data-height");aj=aj?aj:"";ak.css("height",aj)}g(this).css("height",aj)})};var ah=function(ai,aj){if(ai=="fast"){ai=200}else{if(ai=="slow"){ai=600}else{if(!S(ai)){ai=parseInt(ai);if(isNaN(ai)){return 0}}}}return ai*B[aj].time};var S=function(ai){return typeof ai=="number"}};g(document).ready(function(){UrvanovSyntaxHighlighterSyntax.init()})})(jQueryUrvanovSyntaxHighlighter);