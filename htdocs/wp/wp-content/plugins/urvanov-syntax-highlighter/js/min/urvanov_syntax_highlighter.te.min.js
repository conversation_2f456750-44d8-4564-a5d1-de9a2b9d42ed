var jQueryUrvanovSyntaxHighlighter=jQuery;(function(a){UrvanovSyntaxHighlighterUtil=new function(){var c=this;var b=null;c.init=function(){b=UrvanovSyntaxHighlighterSyntaxSettings;c.initGET()};c.addPrefixToID=function(d){return d.replace(/^([#.])?(.*)$/,"$1"+b.prefix+"$2")};c.removePrefixFromID=function(e){var d=new RegExp("^[#.]?"+b.prefix,"i");return e.replace(d,"")};c.cssElem=function(d){return a(c.addPrefixToID(d))};c.setDefault=function(e,f){return(typeof e=="undefined")?f:e};c.setMax=function(e,d){return e<=d?e:d};c.setMin=function(d,e){return d>=e?d:e};c.setRange=function(e,f,d){return c.setMax(c.setMin(e,f),d)};c.getExt=function(e){if(e.indexOf(".")==-1){return undefined}var d=e.split(".");if(d.length){d=d[d.length-1]}else{d=""}return d};c.initGET=function(){window.currentURL=window.location.protocol+"//"+window.location.host+window.location.pathname;window.currentDir=window.currentURL.substring(0,window.currentURL.lastIndexOf("/"));function d(e){e=e.split("+").join(" ");var h={},g,f=/[?&]?([^=]+)=([^&]*)/g;while(g=f.exec(e)){h[decodeURIComponent(g[1])]=decodeURIComponent(g[2])}return h}window.GET=d(document.location.search)};c.getAJAX=function(d,e){d.version=b.version;a.get(b.ajaxurl,d,e)};c.htmlToElements=function(d){return a.parseHTML(d,document,true)};c.postAJAX=function(d,e){d.version=b.version;a.post(b.ajaxurl,d,e)};c.reload=function(){var d="?";for(var e in window.GET){d+=e+"="+window.GET[e]+"&"}window.location=window.currentURL+d};c.escape=function(d){if(typeof encodeURIComponent=="function"){return encodeURIComponent(d)}else{if(typeof escape!="function"){return escape(d)}else{return d}}};c.log=function(d){if(typeof console!="undefined"&&b&&b.debug){console.log(d)}};c.decode_html=function(d){return String(d).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};c.encode_html=function(d){return String(d).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")};c.getReadableColor=function(g,f){f=a.extend({amount:0.5,xMulti:1,yMulti:1.5,normalizeHue:[20,180],normalizeHueXMulti:1/2.5,normalizeHueYMulti:1},f);var d=tinycolor(g);var e=d.toHsv();var i={x:e.s,y:1-e.v};i.x*=f.xMulti;i.y*=f.yMulti;if(f.normalizeHue&&e.h>f.normalizeHue[0]&&e.h<f.normalizeHue[1]){i.x*=f.normalizeHueXMulti;i.y*=f.normalizeHueYMulti}var h=Math.sqrt(Math.pow(i.x,2)+Math.pow(i.y,2));if(h<f.amount){e.v=0}else{e.v=1}e.s=0;return tinycolor(e).toHexString()};c.removeChars=function(e,f){var d=new RegExp("["+e+"]","gmi");return f.replace(d,"")}};a(document).ready(function(){UrvanovSyntaxHighlighterUtil.init()});a.fn.bindFirst=function(c,e){this.bind(c,e);var b=this.data("events")[c.split(".")[0]];var d=b.pop();b.splice(0,0,d)};a.keys=function(d){var c=[];for(var b in d){c.push(b)}return c};RegExp.prototype.execAll=function(c){var f=[];var b=null;while((b=this.exec(c))!=null){var e=[];for(var d in b){if(parseInt(d)==d){e.push(b[d])}}f.push(e)}return f};RegExp.prototype.escape=function(b){return b.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")};String.prototype.sliceReplace=function(d,b,c){return this.substring(0,d)+c+this.substring(b)};String.prototype.escape=function(){var b={"&":"&amp;","<":"&lt;",">":"&gt;"};return this.replace(/[&<>]/g,function(c){return b[c]||c})};String.prototype.linkify=function(b){b=typeof b!="undefined"?b:"";return this.replace(/(http(s)?:\/\/(\S)+)/gmi,'<a href="$1" target="'+b+'">$1</a>')};String.prototype.toTitleCase=function(){var b=this.split(/\s+/);var c="";a.each(b,function(e,d){if(d!=""){c+=d.slice(0,1).toUpperCase()+d.slice(1,d.length);if(e!=b.length-1&&b[e+1]!=""){c+=" "}}});return c}})(jQueryUrvanovSyntaxHighlighter);jqueryPopup=Object();jqueryPopup.defaultSettings={centerBrowser:0,centerScreen:0,height:500,left:0,location:0,menubar:0,resizable:0,scrollbars:0,status:0,width:500,windowName:null,windowURL:null,top:0,toolbar:0,data:null,event:"click"};(function(a){popupWindow=function(d,c,f,b){f=typeof f!=="undefined"?f:null;b=typeof b!=="undefined"?b:null;if(typeof d=="string"){d=jQuery(d)}if(!(d instanceof jQuery)){return false}var e=jQuery.extend({},jqueryPopup.defaultSettings,c||{});d.handler=jQuery(d).bind(e.event,function(){if(f){f()}var g="height="+e.height+",width="+e.width+",toolbar="+e.toolbar+",scrollbars="+e.scrollbars+",status="+e.status+",resizable="+e.resizable+",location="+e.location+",menuBar="+e.menubar;e.windowName=e.windowName||jQuery(this).attr("name");var h=jQuery(this).attr("href");if(!e.windowURL&&!(h=="#")&&!(h=="")){e.windowURL=jQuery(this).attr("href")}var i,j;var k=null;if(e.centerBrowser){if(typeof window.screenY=="undefined"){i=(window.screenTop-120)+((((document.documentElement.clientHeight+120)/2)-(e.height/2)));j=window.screenLeft+((((document.body.offsetWidth+20)/2)-(e.width/2)))}else{i=window.screenY+(((window.outerHeight/2)-(e.height/2)));j=window.screenX+(((window.outerWidth/2)-(e.width/2)))}k=window.open(e.windowURL,e.windowName,g+",left="+j+",top="+i)}else{if(e.centerScreen){i=(screen.height-e.height)/2;j=(screen.width-e.width)/2;k=window.open(e.windowURL,e.windowName,g+",left="+j+",top="+i)}else{k=window.open(e.windowURL,e.windowName,g+",left="+e.left+",top="+e.top)}}if(k!=null){k.focus();if(e.data){k.document.write(e.data)}}if(b){b()}});return e};popdownWindow=function(b,c){if(typeof c=="undefined"){c="click"}b=jQuery(b);if(!(b instanceof jQuery)){return false}b.unbind(c,b.handler)}})(jQueryUrvanovSyntaxHighlighter);(function(g){g.fn.exists=function(){return this.length!==0};g.fn.style=function(B,E,A){var D=this.get(0);if(typeof D=="undefined"){return}var C=D.style;if(typeof B!="undefined"){if(typeof E!="undefined"){A=typeof A!="undefined"?A:"";if(typeof C.setProperty!="undefined"){C.setProperty(B,E,A)}else{C[B]=E}}else{return C[B]}}else{return C}};var e="crayon-pressed";var a="";var j="div.urvanov-syntax-highlighter-syntax";var y=".crayon-toolbar";var k=".crayon-info";var i=".urvanov-syntax-highlighter-plain";var u=".urvanov-syntax-highlighter-main";var x=".crayon-table";var t=".urvanov-syntax-highlighter-loading";var p=".urvanov-syntax-highlighter-code";var d=".crayon-title";var w=".crayon-tools";var h=".crayon-nums";var o=".crayon-num";var b=".crayon-line";var c="urvanov-syntax-highlighter-wrapped";var v=".urvanov-syntax-highlighter-nums-content";var n=".urvanov-syntax-highlighter-nums-button";var s=".urvanov-syntax-highlighter-wrap-button";var r=".urvanov-syntax-highlighter-expand-button";var q="urvanov-syntax-highlighter-expanded urvanov-syntax-highlighter-toolbar-visible";var f="urvanov-syntax-highlighter-placeholder";var z=".urvanov-syntax-highlighter-popup-button";var l=".urvanov-syntax-highlighter-copy-button";var m=".urvanov-syntax-highlighter-plain-button";UrvanovSyntaxHighlighterSyntax=new function(){var J=this;var B=new Object();var ag;var H;var G=0;var Z;J.init=function(){if(typeof B=="undefined"){B=new Object()}ag=UrvanovSyntaxHighlighterSyntaxSettings;H=UrvanovSyntaxHighlighterSyntaxStrings;g(j).each(function(){J.process(this)})};J.process=function(aD,aE){aD=g(aD);var ar=aD.attr("id");if(ar=="urvanov-syntax-highlighter-"){ar+=X()}aD.attr("id",ar);UrvanovSyntaxHighlighterUtil.log(ar);if(typeof aE=="undefined"){aE=false}if(!aE&&!aa(ar)){return}var au=aD.find(y);var aC=aD.find(k);var ap=aD.find(i);var aq=aD.find(u);var aB=aD.find(x);var aj=aD.find(p);var aG=aD.find(d);var aA=aD.find(w);var ay=aD.find(h);var av=aD.find(v);var az=aD.find(n);var am=aD.find(s);var ao=aD.find(r);var aF=aD.find(z);var at=aD.find(l);var al=aD.find(m);B[ar]=aD;B[ar].toolbar=au;B[ar].plain=ap;B[ar].info=aC;B[ar].main=aq;B[ar].table=aB;B[ar].code=aj;B[ar].title=aG;B[ar].tools=aA;B[ar].nums=ay;B[ar].nums_content=av;B[ar].numsButton=az;B[ar].wrapButton=am;B[ar].expandButton=ao;B[ar].popup_button=aF;B[ar].copy_button=at;B[ar].plainButton=al;B[ar].numsVisible=true;B[ar].wrapped=false;B[ar].plainVisible=false;B[ar].toolbar_delay=0;B[ar].time=1;g(i).css("z-index",0);var aw=aq.style();B[ar].mainStyle={height:aw&&aw.height||"","max-height":aw&&aw.maxHeight||"","min-height":aw&&aw.minHeight||"",width:aw&&aw.width||"","max-width":aw&&aw.maxWidth||"","min-width":aw&&aw.minWidth||""};B[ar].mainHeightAuto=B[ar].mainStyle.height==""&&B[ar].mainStyle["max-height"]=="";var ak;var ax=0;B[ar].loading=true;B[ar].scrollBlockFix=false;az.click(function(){UrvanovSyntaxHighlighterSyntax.toggleNums(ar)});am.click(function(){UrvanovSyntaxHighlighterSyntax.toggleWrap(ar)});ao.click(function(){UrvanovSyntaxHighlighterSyntax.toggleExpand(ar)});al.click(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar)});at.click(function(){UrvanovSyntaxHighlighterSyntax.copyPlain(ar)});A(ar);var an=function(){if(ay.filter('[data-settings~="hide"]').length!=0){av.ready(function(){UrvanovSyntaxHighlighterUtil.log("function"+ar);UrvanovSyntaxHighlighterSyntax.toggleNums(ar,true,true)})}else{ac(ar)}if(typeof B[ar].expanded=="undefined"){if(Math.abs(B[ar].main.outerWidth()-B[ar].table.outerWidth())<10){B[ar].expandButton.hide()}else{B[ar].expandButton.show()}}if(ax==5){clearInterval(ak);B[ar].loading=false}ax++};ak=setInterval(an,300);C(ar);g(o,B[ar]).each(function(){var aJ=g(this).attr("data-line");var aI=g("#"+aJ);var aH=aI.style("height");if(aH){aI.attr("data-height",aH)}});aq.css("position","relative");aq.css("z-index",1);Z=(aD.filter('[data-settings~="touchscreen"]').length!=0);if(!Z){aq.click(function(){I(ar,"",false)});ap.click(function(){I(ar,"",false)});aC.click(function(){I(ar,"",false)})}if(aD.filter('[data-settings~="no-popup"]').length==0){B[ar].popup_settings=popupWindow(aF,{height:screen.height-200,width:screen.width-100,top:75,left:50,scrollbars:1,windowURL:"",data:""},function(){F(ar)},function(){})}ap.css("opacity",0);B[ar].toolbarVisible=true;B[ar].hasOneLine=aB.outerHeight()<au.outerHeight()*2;B[ar].toolbarMouseover=false;if(au.filter('[data-settings~="mouseover"]').length!=0&&!Z){B[ar].toolbarMouseover=true;B[ar].toolbarVisible=false;au.css("margin-top","-"+au.outerHeight()+"px");au.hide();if(au.filter('[data-settings~="overlay"]').length!=0&&!B[ar].hasOneLine){au.css("position","absolute");au.css("z-index",2);if(au.filter('[data-settings~="hide"]').length!=0){aq.click(function(){T(ar,undefined,undefined,0)});ap.click(function(){T(ar,false,undefined,0)})}}else{au.css("z-index",4)}if(au.filter('[data-settings~="delay"]').length!=0){B[ar].toolbar_delay=500}aD.mouseenter(function(){T(ar,true)}).mouseleave(function(){T(ar,false)})}else{if(Z){au.show()}}if(aD.filter('[data-settings~="minimize"]').length==0){J.minimize(ar)}if(ap.length!=0&&!Z){if(ap.filter('[data-settings~="dblclick"]').length!=0){aq.dblclick(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar)})}else{if(ap.filter('[data-settings~="click"]').length!=0){aq.click(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar)})}else{if(ap.filter('[data-settings~="mouseover"]').length!=0){aD.mouseenter(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar,true)}).mouseleave(function(){UrvanovSyntaxHighlighterSyntax.togglePlain(ar,false)});az.hide()}}}if(ap.filter('[data-settings~="show-plain-default"]').length!=0){UrvanovSyntaxHighlighterSyntax.togglePlain(ar,true)}}var ai=aD.filter('[data-settings~="expand"]').length!=0;if(!Z&&aD.filter('[data-settings~="scroll-mouseover"]').length!=0){aq.css("overflow","hidden");ap.css("overflow","hidden");aD.mouseenter(function(){N(ar,true,ai)}).mouseleave(function(){N(ar,false,ai)})}if(ai){aD.mouseenter(function(){D(ar,true)}).mouseleave(function(){D(ar,false)})}if(aD.filter('[data-settings~="disable-anim"]').length!=0){B[ar].time=0}if(aD.filter('[data-settings~="wrap"]').length!=0){B[ar].wrapped=true}B[ar].mac=aD.hasClass("urvanov-syntax-highlighter-os-mac");ac(ar);ab(ar);Y(ar)};var aa=function(ai){UrvanovSyntaxHighlighterUtil.log(B);if(typeof B[ai]=="undefined"){B[ai]=g("#"+ai);UrvanovSyntaxHighlighterUtil.log("make "+ai);return true}UrvanovSyntaxHighlighterUtil.log("no make "+ai);return false};var X=function(){return G++};var F=function(ai){if(typeof B[ai]=="undefined"){return aa(ai)}var aj=B[ai].popup_settings;if(aj&&aj.data){return}var al=B[ai].clone(true);al.removeClass("urvanov-syntax-highlighter-wrapped");if(B[ai].wrapped){g(o,al).each(function(){var ao=g(this).attr("data-line");var an=g("#"+ao);var am=an.attr("data-height");am=am?am:"";if(typeof am!="undefined"){an.css("height",am);g(this).css("height",am)}})}al.find(u).css("height","");var ak="";if(B[ai].plainVisible){ak=al.find(i)}else{ak=al.find(u)}aj.data=J.getAllCSS()+'<body class="urvanov-syntax-highlighter-popup-window" style="padding:0; margin:0;"><div class="'+al.attr("class")+' urvanov-syntax-highlighter-popup">'+J.removeCssInline(J.getHtmlString(ak))+"</div></body>"};J.minimize=function(al){var ak=g('<div class="urvanov-syntax-highlighter-minimize urvanov-syntax-highlighter-button"><div>');B[al].tools.append(ak);B[al].origTitle=B[al].title.html();if(!B[al].origTitle){B[al].title.html(H.minimize)}var aj="urvanov-syntax-highlighter-minimized";var ai=function(){B[al].toolbarPreventHide=false;ak.remove();B[al].removeClass(aj);B[al].title.html(B[al].origTitle);var am=B[al].toolbar;if(am.filter('[data-settings~="never-show"]').length!=0){am.remove()}};B[al].toolbar.click(ai);ak.click(ai);B[al].addClass(aj);B[al].toolbarPreventHide=true;T(al,undefined,undefined,0)};J.getHtmlString=function(ai){return g("<div>").append(ai.clone()).remove().html()};J.removeCssInline=function(ak){var aj=/style\s*=\s*"([^"]+)"/gmi;var ai=null;while((ai=aj.exec(ak))!=null){var al=ai[1];al=al.replace(/\b(?:width|height)\s*:[^;]+;/gmi,"");ak=ak.sliceReplace(ai.index,ai.index+ai[0].length,'style="'+al+'"')}return ak};J.getAllCSS=function(){var ak="";var aj=g('link[rel="stylesheet"]');var ai=[];if(aj.length==1){ai=aj}else{ai=aj.filter('[href*="urvanov-syntax-highlighter"], [href*="min/"]')}ai.each(function(){var al=J.getHtmlString(g(this));ak+=al});return ak};J.copyPlain=function(ak,al){if(typeof B[ak]=="undefined"){return aa(ak)}var aj=B[ak].plain;var ai=aj[0].value;navigator.clipboard.writeText(ai).then(function(am){return I(ak,H.copy)});return false};var I=function(aj,al,ai){if(typeof B[aj]=="undefined"){return aa(aj)}var ak=B[aj].info;if(typeof al=="undefined"){al=""}if(typeof ai=="undefined"){ai=true}if(M(ak)&&ai){ak.html("<div>"+al+"</div>");ak.css("margin-top",-ak.outerHeight());ak.show();R(aj,ak,true);setTimeout(function(){R(aj,ak,false)},5000)}if(!ai){R(aj,ak,false)}};var A=function(ai){if(window.devicePixelRatio>1){var aj=g(".urvanov-syntax-highlighter-button-icon",B[ai].toolbar);aj.each(function(){var al=g(this).css("background-image");var ak=al.replace(/\.(?=[^\.]+$)/g,"@2x.");g(this).css("background-size","48px 128px");g(this).css("background-image",ak)})}};var M=function(ai){var aj="-"+ai.outerHeight()+"px";if(ai.css("margin-top")==aj||ai.css("display")=="none"){return true}else{return false}};var R=function(al,ak,aj,an,am,ap){var ai=function(){if(ap){ap(al,ak)}};var ao="-"+ak.outerHeight()+"px";if(typeof aj=="undefined"){if(M(ak)){aj=true}else{aj=false}}if(typeof an=="undefined"){an=100}if(an==false){an=false}if(typeof am=="undefined"){am=0}ak.stop(true);if(aj==true){ak.show();ak.animate({marginTop:0},ah(an,al),ai)}else{if(aj==false){if(ak.css("margin-top")=="0px"&&am){ak.delay(am)}ak.animate({marginTop:ao},ah(an,al),function(){ak.hide();ai()})}}};J.togglePlain=function(al,am,aj){if(typeof B[al]=="undefined"){return aa(al)}var ai=B[al].main;var ak=B[al].plain;if((ai.is(":animated")||ak.is(":animated"))&&typeof am=="undefined"){return}ae(al);var ao,an;if(typeof am!="undefined"){if(am){ao=ai;an=ak}else{ao=ak;an=ai}}else{if(ai.css("z-index")==1){ao=ai;an=ak}else{ao=ak;an=ai}}B[al].plainVisible=(an==ak);B[al].top=ao.scrollTop();B[al].left=ao.scrollLeft();B[al].scrollChanged=false;C(al);ao.stop(true);ao.fadeTo(ah(500,al),0,function(){ao.css("z-index",0)});an.stop(true);an.fadeTo(ah(500,al),1,function(){an.css("z-index",1);if(an==ak){if(aj){ak.select()}else{}}an.scrollTop(B[al].top+1);an.scrollTop(B[al].top);an.scrollLeft(B[al].left+1);an.scrollLeft(B[al].left)});an.scrollTop(B[al].top);an.scrollLeft(B[al].left);ab(al);T(al,false);return false};J.toggleNums=function(am,al,ai){if(typeof B[am]=="undefined"){aa(am);return false}if(B[am].table.is(":animated")){return false}var ao=Math.round(B[am].nums_content.outerWidth()+1);var an="-"+ao+"px";var ak;if(typeof al!="undefined"){ak=false}else{ak=(B[am].table.css("margin-left")==an)}var aj;if(ak){aj="0px";B[am].numsVisible=true}else{B[am].table.css("margin-left","0px");B[am].numsVisible=false;aj=an}if(typeof ai!="undefined"){B[am].table.css("margin-left",aj);ac(am);return false}var ap=(B[am].table.outerWidth()+K(B[am].table.css("margin-left"))>B[am].main.outerWidth());var aq=(B[am].table.outerHeight()>B[am].main.outerHeight());if(!ap&&!aq){B[am].main.css("overflow","hidden")}B[am].table.animate({marginLeft:aj},ah(200,am),function(){if(typeof B[am]!="undefined"){ac(am);if(!ap&&!aq){B[am].main.css("overflow","auto")}}});return false};J.toggleWrap=function(ai){B[ai].wrapped=!B[ai].wrapped;Y(ai)};J.toggleExpand=function(ai){var aj=!UrvanovSyntaxHighlighterUtil.setDefault(B[ai].expanded,false);D(ai,aj)};var Y=function(ai,aj){aj=UrvanovSyntaxHighlighterUtil.setDefault(aj,true);if(B[ai].wrapped){B[ai].addClass(c)}else{B[ai].removeClass(c)}E(ai);if(!B[ai].expanded&&aj){V(ai)}B[ai].wrapTimes=0;clearInterval(B[ai].wrapTimer);B[ai].wrapTimer=setInterval(function(){if(B[ai].is(":visible")){O(ai);B[ai].wrapTimes++;if(B[ai].wrapTimes==5){clearInterval(B[ai].wrapTimer)}}},200)};var ad=function(ai){if(typeof B[ai]=="undefined"){aa(ai);return false}};var K=function(aj){if(typeof aj!="string"){return 0}var ai=aj.replace(/[^-0-9]/g,"");if(ai.length==0){return 0}else{return parseInt(ai)}};var ac=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].numsVisible=="undefined"){return}if(B[ai].numsVisible){B[ai].numsButton.removeClass(a);B[ai].numsButton.addClass(e)}else{B[ai].numsButton.removeClass(e);B[ai].numsButton.addClass(a)}};var E=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].wrapped=="undefined"){return}if(B[ai].wrapped){B[ai].wrapButton.removeClass(a);B[ai].wrapButton.addClass(e)}else{B[ai].wrapButton.removeClass(e);B[ai].wrapButton.addClass(a)}};var W=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].expanded=="undefined"){return}if(B[ai].expanded){B[ai].expandButton.removeClass(a);B[ai].expandButton.addClass(e)}else{B[ai].expandButton.removeClass(e);B[ai].expandButton.addClass(a)}};var ab=function(ai){if(typeof B[ai]=="undefined"||typeof B[ai].plainVisible=="undefined"){return}if(B[ai].plainVisible){B[ai].plainButton.removeClass(a);B[ai].plainButton.addClass(e)}else{B[ai].plainButton.removeClass(e);B[ai].plainButton.addClass(a)}};var T=function(aj,ai,al,ak){if(typeof B[aj]=="undefined"){return aa(aj)}else{if(!B[aj].toolbarMouseover){return}else{if(ai==false&&B[aj].toolbarPreventHide){return}else{if(Z){return}}}}var am=B[aj].toolbar;if(typeof ak=="undefined"){ak=B[aj].toolbar_delay}R(aj,am,ai,al,ak,function(){B[aj].toolbarVisible=ai})};var Q=function(ak,ai){var aj=g.extend({},ak);aj.width+=ai.width;aj.height+=ai.height;return aj};var P=function(ak,ai){var aj=g.extend({},ak);aj.width-=ai.width;aj.height-=ai.height;return aj};var U=function(ai){if(typeof B[ai].initialSize=="undefined"){B[ai].toolbarHeight=B[ai].toolbar.outerHeight();B[ai].innerSize={width:B[ai].width(),height:B[ai].height()};B[ai].outerSize={width:B[ai].outerWidth(),height:B[ai].outerHeight()};B[ai].borderSize=P(B[ai].outerSize,B[ai].innerSize);B[ai].initialSize={width:B[ai].main.outerWidth(),height:B[ai].main.outerHeight()};B[ai].initialSize.height+=B[ai].toolbarHeight;B[ai].initialOuterSize=Q(B[ai].initialSize,B[ai].borderSize);B[ai].finalSize={width:B[ai].table.outerWidth(),height:B[ai].table.outerHeight()};B[ai].finalSize.height+=B[ai].toolbarHeight;B[ai].finalSize.width=UrvanovSyntaxHighlighterUtil.setMin(B[ai].finalSize.width,B[ai].initialSize.width);B[ai].finalSize.height=UrvanovSyntaxHighlighterUtil.setMin(B[ai].finalSize.height,B[ai].initialSize.height);B[ai].diffSize=P(B[ai].finalSize,B[ai].initialSize);B[ai].finalOuterSize=Q(B[ai].finalSize,B[ai].borderSize);B[ai].initialSize.height+=B[ai].toolbar.outerHeight()}};var D=function(al,ao){if(typeof B[al]=="undefined"){return aa(al)}if(typeof ao=="undefined"){return}var aj=B[al].main;var aq=B[al].plain;if(ao){if(typeof B[al].expanded=="undefined"){U(al);B[al].expandTime=UrvanovSyntaxHighlighterUtil.setRange(B[al].diffSize.width/3,300,800);B[al].expanded=false;var ap=B[al].finalOuterSize;B[al].placeholder=g("<div></div>");B[al].placeholder.addClass(f);B[al].placeholder.css(ap);B[al].before(B[al].placeholder);B[al].placeholder.css("margin",B[al].css("margin"));g(window).bind("resize",L)}var am={height:"auto","min-height":"none","max-height":"none"};var ai={width:"auto","min-width":"none","max-width":"none"};B[al].outerWidth(B[al].outerWidth());B[al].css({"min-width":"none","max-width":"none"});var an={width:B[al].finalOuterSize.width};if(!B[al].mainHeightAuto&&!B[al].hasOneLine){an.height=B[al].finalOuterSize.height;B[al].outerHeight(B[al].outerHeight())}aj.css(am);aj.css(ai);B[al].stop(true);B[al].animate(an,ah(B[al].expandTime,al),function(){B[al].expanded=true;W(al)});B[al].placeholder.show();g("body").prepend(B[al]);B[al].addClass(q);L()}else{var ar=B[al].initialOuterSize;var ak=B[al].toolbar_delay;if(ar){B[al].stop(true);if(!B[al].expanded){B[al].delay(ak)}var an={width:ar.width};if(!B[al].mainHeightAuto&&!B[al].hasOneLine){an.height=ar.height}B[al].animate(an,ah(B[al].expandTime,al),function(){af(al)})}else{setTimeout(function(){af(al)},ak)}B[al].placeholder.hide();B[al].placeholder.before(B[al]);B[al].css({left:"auto",top:"auto"});B[al].removeClass(q)}ae(al);if(ao){Y(al,false)}};var L=function(){for(uid in B){if(B[uid].hasClass(q)){B[uid].css(B[uid].placeholder.offset())}}};var af=function(ai){B[ai].expanded=false;V(ai);W(ai);if(B[ai].wrapped){Y(ai)}};var N=function(al,aj,am){if(typeof B[al]=="undefined"){return aa(al)}if(typeof aj=="undefined"||am||B[al].expanded){return}var ai=B[al].main;var ak=B[al].plain;if(aj){ai.css("overflow","auto");ak.css("overflow","auto");if(typeof B[al].top!="undefined"){visible=(ai.css("z-index")==1?ai:ak);visible.scrollTop(B[al].top-1);visible.scrollTop(B[al].top);visible.scrollLeft(B[al].left-1);visible.scrollLeft(B[al].left)}}else{visible=(ai.css("z-index")==1?ai:ak);B[al].top=visible.scrollTop();B[al].left=visible.scrollLeft();ai.css("overflow","hidden");ak.css("overflow","hidden")}B[al].scrollChanged=true;C(al)};var C=function(ai){B[ai].table.style("width","100%","important");var aj=setTimeout(function(){B[ai].table.style("width","");clearInterval(aj)},10)};var V=function(ak){var aj=B[ak].main;var ai=B[ak].mainStyle;aj.css(ai);B[ak].css("height","auto");B[ak].css("width",ai.width);B[ak].css("max-width",ai["max-width"]);B[ak].css("min-width",ai["min-width"])};var ae=function(ai){B[ai].plain.outerHeight(B[ai].main.outerHeight())};var O=function(ai){g(o,B[ai]).each(function(){var al=g(this).attr("data-line");var ak=g("#"+al);var aj=null;if(B[ai].wrapped){ak.css("height","");aj=ak.outerHeight();aj=aj?aj:""}else{aj=ak.attr("data-height");aj=aj?aj:"";ak.css("height",aj)}g(this).css("height",aj)})};var ah=function(ai,aj){if(ai=="fast"){ai=200}else{if(ai=="slow"){ai=600}else{if(!S(ai)){ai=parseInt(ai);if(isNaN(ai)){return 0}}}}return ai*B[aj].time};var S=function(ai){return typeof ai=="number"}};g(document).ready(function(){UrvanovSyntaxHighlighterSyntax.init()})})(jQueryUrvanovSyntaxHighlighter);(function(b){var a=UrvanovSyntaxHighlighterTagEditorSettings;window.urvanovSyntaxHighlighterQuickTags=new function(){var c=this;c.init=function(){c.sel='*[id*="urvanov_syntax_highlighter_quicktag"],*[class*="urvanov_syntax_highlighter_quicktag"]';var f=a.quicktag_text;f=f!==undefined?f:"urvanov_syntax_highlighter";QTags.addButton("urvanov_syntax_highlighter_quicktag",f,function(){UrvanovSyntaxHighlighterTagEditor.showDialog({insert:function(g){QTags.insertContent(g)},select:c.getSelectedText,editor_str:"html",output:"encode"});b(c.sel).removeClass("qt_urvanov_syntax_highlighter_highlight")});var d;var e=setInterval(function(){d=b(c.sel).first();if(typeof d!="undefined"){UrvanovSyntaxHighlighterTagEditor.bind(c.sel);clearInterval(e)}},100)};c.getSelectedText=function(){if(QTags.instances.length==0){return null}else{var f=QTags.instances[0];var e=f.canvas.selectionStart;var d=f.canvas.selectionEnd;return f.canvas.value.substring(e,d)}}};b(document).ready(function(){urvanovSyntaxHighlighterQuickTags.init()})})(jQueryUrvanovSyntaxHighlighter);
/*!
 Colorbox v1.5.9 - 2014-04-25
 jQuery lightbox and modal window plugin
 (c) 2014 Jack Moore - http://www.jacklmoore.com/colorbox
 license: http://www.opensource.org/licenses/mit-license.php
 */
(function(aT,a8,a4){function aZ(a,d,c){var b=a8.createElement(a);return d&&(b.id=ab+d),c&&(b.style.cssText=c),aT(b)}function aY(){return a4.innerHeight?a4.innerHeight:aT(a4).height()}function aV(b,a){a!==Object(a)&&(a={}),this.cache={},this.el=b,this.value=function(c){var d;return void 0===this.cache[c]&&(d=aT(this.el).attr("data-cbox-"+c),void 0!==d?this.cache[c]=d:void 0!==a[c]?this.cache[c]=a[c]:void 0!==ad[c]&&(this.cache[c]=ad[c])),this.cache[c]},this.get=function(d){var c=this.value(d);return aT.isFunction(c)?c.call(this.el,this):c}}function a5(b){var c=ag.length,a=(aM+b)%c;return 0>a?c+a:a}function bd(a,b){return Math.round((/%/.test(a)?("x"===b?az.width():aY())/100:1)*parseInt(a,10))}function aU(a,b){return a.get("photo")||a.get("photoRegex").test(b)}function a1(a,b){return a.get("retinaUrl")&&a4.devicePixelRatio>1?b.replace(a.get("photoRegex"),a.get("retinaSuffix")):b}function a9(a){"contains" in aP[0]&&!aP[0].contains(a.target)&&a.target!==aR[0]&&(a.stopPropagation(),aP.focus())}function ba(a){ba.str!==a&&(aP.add(aR).removeClass(ba.str).addClass(a),ba.str=a)}function a6(a){aM=0,a&&a!==!1?(ag=aT("."+af).filter(function(){var b=aT.data(this,ac),c=new aV(this,b);return c.get("rel")===a}),aM=ag.index(bf.el),-1===aM&&(ag=ag.add(bf.el),aM=ag.length-1)):ag=aT(bf.el)}function aS(a){aT(a8).trigger(a),aA.triggerHandler(a)}function a7(b){var g;if(!ax){if(g=aT(b).data("colorbox"),bf=new aV(b,g),a6(bf.get("rel")),!aH){aH=aW=!0,ba(bf.get("className")),aP.css({visibility:"hidden",display:"block",opacity:""}),ar=aZ(aG,"LoadedContent","width:0; height:0; overflow:hidden; visibility:hidden"),bb.css({width:"",height:""}).append(ar),aB=aj.height()+a2.height()+bb.outerHeight(!0)-bb.height(),a3=aC.width()+aw.width()+bb.outerWidth(!0)-bb.width(),aF=ar.outerHeight(!0),ap=ar.outerWidth(!0);var d=bd(bf.get("initialWidth"),"x"),c=bd(bf.get("initialHeight"),"y"),a=bf.get("maxWidth"),e=bf.get("maxHeight");bf.w=(a!==!1?Math.min(d,bd(a,"x")):d)-ap-a3,bf.h=(e!==!1?Math.min(c,bd(e,"y")):c)-aF-aB,ar.css({width:"",height:bf.h}),au.position(),aS(bc),bf.get("onOpen"),ao.add(av).hide(),aP.focus(),bf.get("trapFocus")&&a8.addEventListener&&(a8.addEventListener("focus",a9,!0),aA.one(aO,function(){a8.removeEventListener("focus",a9,!0)})),bf.get("returnFocus")&&aA.one(aO,function(){aT(bf.el).focus()})}aR.css({opacity:parseFloat(bf.get("opacity"))||"",cursor:bf.get("overlayClose")?"pointer":"",visibility:"visible"}).show(),bf.get("closeButton")?aE.html(bf.get("close")).appendTo(bb):aE.appendTo("<div/>"),aQ()}}function aX(){!aP&&a8.body&&(ah=!1,az=aT(a4),aP=aZ(aG).attr({id:ac,"class":aT.support.opacity===!1?ab+"IE":"",role:"dialog",tabindex:"-1"}).hide(),aR=aZ(aG,"Overlay").hide(),ak=aT([aZ(aG,"LoadingOverlay")[0],aZ(aG,"LoadingGraphic")[0]]),aN=aZ(aG,"Wrapper"),bb=aZ(aG,"Content").append(av=aZ(aG,"Title"),al=aZ(aG,"Current"),an=aT('<button type="button"/>').attr({id:ab+"Previous"}),at=aT('<button type="button"/>').attr({id:ab+"Next"}),ay=aZ("button","Slideshow"),ak),aE=aT('<button type="button"/>').attr({id:ab+"Close"}),aN.append(aZ(aG).append(aZ(aG,"TopLeft"),aj=aZ(aG,"TopCenter"),aZ(aG,"TopRight")),aZ(aG,!1,"clear:left").append(aC=aZ(aG,"MiddleLeft"),bb,aw=aZ(aG,"MiddleRight")),aZ(aG,!1,"clear:left").append(aZ(aG,"BottomLeft"),a2=aZ(aG,"BottomCenter"),aZ(aG,"BottomRight"))).find("div div").css({"float":"left"}),aq=aZ(aG,!1,"position:absolute; width:9999px; visibility:hidden; display:none; max-width:none;"),ao=at.add(an).add(al).add(ay),aT(a8.body).append(aR,aP.append(aN,aq)))}function a0(){function a(b){b.which>1||b.shiftKey||b.altKey||b.metaKey||b.ctrlKey||(b.preventDefault(),a7(this))}return aP?(ah||(ah=!0,at.click(function(){au.next()}),an.click(function(){au.prev()}),aE.click(function(){au.close()}),aR.click(function(){bf.get("overlayClose")&&au.close()}),aT(a8).bind("keydown."+ab,function(b){var c=b.keyCode;aH&&bf.get("escKey")&&27===c&&(b.preventDefault(),au.close()),aH&&bf.get("arrowKey")&&ag[1]&&!b.altKey&&(37===c?(b.preventDefault(),an.click()):39===c&&(b.preventDefault(),at.click()))}),aT.isFunction(aT.fn.on)?aT(a8).on("click."+ab,"."+af,a):aT("."+af).live("click."+ab,a)),!0):!1}function aQ(){var f,i,b,a=au.prep,g=++aK;if(aW=!0,ai=!1,aS(be),aS(aJ),bf.get("onLoad"),bf.h=bf.get("height")?bd(bf.get("height"),"y")-aF-aB:bf.get("innerHeight")&&bd(bf.get("innerHeight"),"y"),bf.w=bf.get("width")?bd(bf.get("width"),"x")-ap-a3:bf.get("innerWidth")&&bd(bf.get("innerWidth"),"x"),bf.mw=bf.w,bf.mh=bf.h,bf.get("maxWidth")&&(bf.mw=bd(bf.get("maxWidth"),"x")-ap-a3,bf.mw=bf.w&&bf.w<bf.mw?bf.w:bf.mw),bf.get("maxHeight")&&(bf.mh=bd(bf.get("maxHeight"),"y")-aF-aB,bf.mh=bf.h&&bf.h<bf.mh?bf.h:bf.mh),f=bf.get("href"),am=setTimeout(function(){ak.show()},100),bf.get("inline")){var j=aT(f);b=aT("<div>").hide().insertBefore(j),aA.one(be,function(){b.replaceWith(j)}),a(j)}else{bf.get("iframe")?a(" "):bf.get("html")?a(bf.get("html")):aU(bf,f)?(f=a1(bf,f),ai=new Image,aT(ai).addClass(ab+"Photo").bind("error",function(){a(aZ(aG,"Error").html(bf.get("imgError")))}).one("load",function(){g===aK&&setTimeout(function(){var c;aT.each(["alt","longdesc","aria-describedby"],function(h,d){var k=aT(bf.el).attr(d)||aT(bf.el).attr("data-"+d);k&&ai.setAttribute(d,k)}),bf.get("retinaImage")&&a4.devicePixelRatio>1&&(ai.height=ai.height/a4.devicePixelRatio,ai.width=ai.width/a4.devicePixelRatio),bf.get("scalePhotos")&&(i=function(){ai.height-=ai.height*c,ai.width-=ai.width*c},bf.mw&&ai.width>bf.mw&&(c=(ai.width-bf.mw)/ai.width,i()),bf.mh&&ai.height>bf.mh&&(c=(ai.height-bf.mh)/ai.height,i())),bf.h&&(ai.style.marginTop=Math.max(bf.mh-ai.height,0)/2+"px"),ag[1]&&(bf.get("loop")||ag[aM+1])&&(ai.style.cursor="pointer",ai.onclick=function(){au.next()}),ai.style.width=ai.width+"px",ai.style.height=ai.height+"px",a(ai)},1)}),ai.src=f):f&&aq.load(f,bf.get("data"),function(d,c){g===aK&&a("error"===c?aZ(aG,"Error").html(bf.get("xhrError")):aT(this).contents())})}}var aR,aP,aN,bb,aj,aC,aw,a2,ag,az,ar,aq,ak,av,al,ay,at,an,aE,ao,bf,aB,a3,aF,ap,aM,ai,aH,aW,ax,am,au,ah,ad={html:!1,photo:!1,iframe:!1,inline:!1,transition:"elastic",speed:300,fadeOut:300,width:!1,initialWidth:"600",innerWidth:!1,maxWidth:!1,height:!1,initialHeight:"450",innerHeight:!1,maxHeight:!1,scalePhotos:!0,scrolling:!0,opacity:0.9,preloading:!0,className:!1,overlayClose:!0,escKey:!0,arrowKey:!0,top:!1,bottom:!1,left:!1,right:!1,fixed:!1,data:void 0,closeButton:!0,fastIframe:!0,open:!1,reposition:!0,loop:!0,slideshow:!1,slideshowAuto:!0,slideshowSpeed:2500,slideshowStart:"start slideshow",slideshowStop:"stop slideshow",photoRegex:/\.(gif|png|jp(e|g|eg)|bmp|ico|webp|jxr|svg)((#|\?).*)?$/i,retinaImage:!1,retinaUrl:!1,retinaSuffix:"@2x.$1",current:"image {current} of {total}",previous:"previous",next:"next",close:"close",xhrError:"This content failed to load.",imgError:"This image failed to load.",returnFocus:!0,trapFocus:!0,onOpen:!1,onLoad:!1,onComplete:!1,onCleanup:!1,onClosed:!1,rel:function(){return this.rel},href:function(){return aT(this).attr("href")},title:function(){return this.title}},ac="colorbox",ab="cbox",af=ab+"Element",bc=ab+"_open",aJ=ab+"_load",aa=ab+"_complete",aL=ab+"_cleanup",aO=ab+"_closed",be=ab+"_purge",aA=aT("<a/>"),aG="div",aK=0,aD={},aI=function(){function l(){clearTimeout(g)}function j(){(bf.get("loop")||ag[aM+1])&&(l(),g=setTimeout(au.next,bf.get("slideshowSpeed")))}function f(){ay.html(bf.get("slideshowStop")).unbind(m).one(m,d),aA.bind(aa,j).bind(aJ,l),aP.removeClass(k+"off").addClass(k+"on")}function d(){l(),aA.unbind(aa,j).unbind(aJ,l),ay.html(bf.get("slideshowStart")).unbind(m).one(m,function(){au.next(),f()}),aP.removeClass(k+"on").addClass(k+"off")}function c(){b=!1,ay.hide(),l(),aA.unbind(aa,j).unbind(aJ,l),aP.removeClass(k+"off "+k+"on")}var b,g,k=ab+"Slideshow_",m="click."+ab;return function(){b?bf.get("slideshow")||(aA.unbind(aL,c),c()):bf.get("slideshow")&&ag[1]&&(b=!0,aA.one(aL,c),bf.get("slideshowAuto")?f():d(),ay.show())}}();aT.colorbox||(aT(aX),au=aT.fn[ac]=aT[ac]=function(b,a){var d,c=this;if(b=b||{},aT.isFunction(c)){c=aT("<a/>"),b.open=!0}else{if(!c[0]){return c}}return c[0]?(aX(),a0()&&(a&&(b.onComplete=a),c.each(function(){var e=aT.data(this,ac)||{};aT.data(this,ac,aT.extend(e,b))}).addClass(af),d=new aV(c[0],b),d.get("open")&&a7(c[0])),c):c},au.position=function(o,j){function b(){aj[0].style.width=a2[0].style.width=bb[0].style.width=parseInt(aP[0].style.width,10)-a3+"px",bb[0].style.height=aC[0].style.height=aw[0].style.height=parseInt(aP[0].style.height,10)-aB+"px"}var a,k,t,f=0,p=0,q=aP.offset();if(az.unbind("resize."+ab),aP.css({top:-90000,left:-90000}),k=az.scrollTop(),t=az.scrollLeft(),bf.get("fixed")?(q.top-=k,q.left-=t,aP.css({position:"fixed"})):(f=k,p=t,aP.css({position:"absolute"})),p+=bf.get("right")!==!1?Math.max(az.width()-bf.w-ap-a3-bd(bf.get("right"),"x"),0):bf.get("left")!==!1?bd(bf.get("left"),"x"):Math.round(Math.max(az.width()-bf.w-ap-a3,0)/2),f+=bf.get("bottom")!==!1?Math.max(aY()-bf.h-aF-aB-bd(bf.get("bottom"),"y"),0):bf.get("top")!==!1?bd(bf.get("top"),"y"):Math.round(Math.max(aY()-bf.h-aF-aB,0)/2),aP.css({top:q.top,left:q.left,visibility:"visible"}),aN[0].style.width=aN[0].style.height="9999px",a={width:bf.w+ap+a3,height:bf.h+aF+aB,top:f,left:p},o){var m=0;aT.each(a,function(c){return a[c]!==aD[c]?(m=o,void 0):void 0}),o=m}aD=a,o||aP.css(a),aP.dequeue().animate(a,{duration:o||0,complete:function(){b(),aW=!1,aN[0].style.width=bf.w+ap+a3+"px",aN[0].style.height=bf.h+aF+aB+"px",bf.get("reposition")&&setTimeout(function(){az.bind("resize."+ab,au.position)},1),j&&j()},step:b})},au.resize=function(a){var b;aH&&(a=a||{},a.width&&(bf.w=bd(a.width,"x")-ap-a3),a.innerWidth&&(bf.w=bd(a.innerWidth,"x")),ar.css({width:bf.w}),a.height&&(bf.h=bd(a.height,"y")-aF-aB),a.innerHeight&&(bf.h=bd(a.innerHeight,"y")),a.innerHeight||a.height||(b=ar.scrollTop(),ar.css({height:"auto"}),bf.h=ar.height()),ar.css({height:bf.h}),b&&ar.scrollTop(b),au.position("none"===bf.get("transition")?0:bf.get("speed")))},au.prep=function(c){function h(){return bf.w=bf.w||ar.width(),bf.w=bf.mw&&bf.mw<bf.w?bf.mw:bf.w,bf.w}function b(){return bf.h=bf.h||ar.height(),bf.h=bf.mh&&bf.mh<bf.h?bf.mh:bf.h,bf.h}if(aH){var f,e="none"===bf.get("transition")?0:bf.get("speed");ar.remove(),ar=aZ(aG,"LoadedContent").append(c),ar.hide().appendTo(aq.show()).css({width:h(),overflow:bf.get("scrolling")?"auto":"hidden"}).css({height:b()}).prependTo(bb),aq.hide(),aT(ai).css({"float":"none"}),ba(bf.get("className")),f=function(){function g(){aT.support.opacity===!1&&aP[0].style.removeAttribute("filter")}var k,j,d=ag.length;aH&&(j=function(){clearTimeout(am),ak.hide(),aS(aa),bf.get("onComplete")},av.html(bf.get("title")).show(),ar.show(),d>1?("string"==typeof bf.get("current")&&al.html(bf.get("current").replace("{current}",aM+1).replace("{total}",d)).show(),at[bf.get("loop")||d-1>aM?"show":"hide"]().html(bf.get("next")),an[bf.get("loop")||aM?"show":"hide"]().html(bf.get("previous")),aI(),bf.get("preloading")&&aT.each([a5(-1),a5(1)],function(){var a,p=ag[this],m=new aV(p,aT.data(p,ac)),l=m.get("href");l&&aU(m,l)&&(l=a1(m,l),a=a8.createElement("img"),a.src=l)})):ao.hide(),bf.get("iframe")?(k=a8.createElement("iframe"),"frameBorder" in k&&(k.frameBorder=0),"allowTransparency" in k&&(k.allowTransparency="true"),bf.get("scrolling")||(k.scrolling="no"),aT(k).attr({src:bf.get("href"),name:(new Date).getTime(),"class":ab+"Iframe",allowFullScreen:!0}).one("load",j).appendTo(ar),aA.one(be,function(){k.src="//about:blank"}),bf.get("fastIframe")&&aT(k).trigger("load")):j(),"fade"===bf.get("transition")?aP.fadeTo(e,1,g):g())},"fade"===bf.get("transition")?aP.fadeTo(e,0,function(){au.position(0,f)}):au.position(e,f)}},au.next=function(){!aW&&ag[1]&&(bf.get("loop")||ag[aM+1])&&(aM=a5(1),a7(ag[aM]))},au.prev=function(){!aW&&ag[1]&&(bf.get("loop")||aM)&&(aM=a5(-1),a7(ag[aM]))},au.close=function(){aH&&!ax&&(ax=!0,aH=!1,aS(aL),bf.get("onCleanup"),az.unbind("."+ab),aR.fadeTo(bf.get("fadeOut")||0,0),aP.stop().fadeTo(bf.get("fadeOut")||0,0,function(){aP.hide(),aR.hide(),aS(be),ar.remove(),setTimeout(function(){ax=!1,aS(aO),bf.get("onClosed")},1)}))},au.remove=function(){aP&&(aP.stop(),aT.colorbox.close(),aP.stop().remove(),aR.remove(),ax=!1,aP=null,aT("."+af).removeData(ac).removeClass(af),aT(a8).unbind("click."+ab))},au.element=function(){return aT(bf.el)},au.settings=ad)})(jQuery,document,window);(function(g,c){var a="crayon-inline";var d=c.element.createElement,e=c.blocks.registerBlockType,f={};e("urvanov-syntax-highlighter/code-block",{title:"Urvanov Syntax Highlighter",icon:"editor-code",category:"formatting",attributes:{content:{type:"string",source:"html",selector:"div"}},edit:function(h){var i=h.attributes.content;function j(k){h.setAttributes({content:k})}return d(c.element.Fragment,null,d(c.editor.BlockControls,null,d(c.components.Toolbar,null,d(c.components.IconButton,{icon:"editor-code",title:"UrvanovSyntaxHighlighter",onClick:function(){window.UrvanovSyntaxHighlighterTagEditor.showDialog({update:function(k){},br_html_block_after:"",input:"decode",output:"encode",node:i?UrvanovSyntaxHighlighterUtil.htmlToElements(i)[0]:null,insert:function(k){j(k)}})}},"UrvanovSyntaxHighlighter"))),d("div",{style:f,dangerouslySetInnerHTML:{__html:h.attributes.content==null?'10 REM Your code will be here<br>20 PRINT "HELLO, WORLD!"':h.attributes.content}}))},save:function(h){var i=h.attributes.content;return d("div",{dangerouslySetInnerHTML:{__html:i}})}});var b=function(h){return c.element.createElement(c.editor.RichTextToolbarButton,{icon:"editor-code",title:"UrvanovSyntaxHighlighter",onClick:function(p){var o=c.richText.getActiveFormat(h.value,"urvanov-syntax-highlighter/code-inline");var n=h.value.start;var l=h.value.end;if(o){var m="urvanov-syntax-highlighter/code-inline";while(h.value.formats[n]&&h.value.formats[n].find(function(q){return q.type==m})){n--}n++;l++;while(h.value.formats[l]&&h.value.formats[l].find(function(q){return q.type==m})){l++}var k=c.richText.slice(h.value,n,l);var i=c.richText.toHTMLString({value:k});var j=UrvanovSyntaxHighlighterUtil.htmlToElements(i)[0]}else{var k=c.richText.slice(h.value,n,l);var i='<span class="'+a+'">'+c.richText.toHTMLString({value:k})+"</span>";var j=UrvanovSyntaxHighlighterUtil.htmlToElements(i)[0]}window.UrvanovSyntaxHighlighterTagEditor.showDialog({update:function(q){},node:j,input:"decode",output:"encode",insert:function(q){h.onChange(c.richText.insert(h.value,c.richText.create({html:q}),n,l))}})},isActive:h.isActive})};c.richText.registerFormatType("urvanov-syntax-highlighter/code-inline",{title:"UrvanovSyntaxHighlighter",tagName:"span",className:a,edit:b});window.UrvanovSyntaxHighlighterTagEditor=new function(){var o=this;var H=false;var E=false;var j=false;var v,k,y,G,C;var J,z,n,B;var D=0;var p,x;var I=null;var l="";var K=false;var t,w,i;var F,h,A,q,m;var u;var r={inline:true,width:690,height:"90%",closeButton:false,fixed:true,transition:"none",className:"urvanov-syntax-highlighter-colorbox",onOpen:function(){g(this.outer).prepend(g(t.bar_content))},onComplete:function(){g(t.code_css).focus()},onCleanup:function(){g(t.bar).prepend(g(t.bar_content))}};o.init=function(){t=UrvanovSyntaxHighlighterTagEditorSettings;w=UrvanovSyntaxHighlighterSyntaxSettings;i=UrvanovSyntaxHighlighterUtil;r.href=t.content_css};o.bind=function(L){if(!H){H=true;o.init()}var s=g(L);s.each(function(N,M){var P=g(M);var O=g('<a class="urvanov-syntax-highlighter-tag-editor-button-wrapper"></a>').attr("href",t.content_css);P.after(O);O.append(P);O.colorbox(r)})};o.hide=function(){g.colorbox.close();return false};o.loadDialog=function(s){if(!E){E=true}else{s&&s();return}UrvanovSyntaxHighlighterUtil.getAJAX({action:"urvanov-syntax-highlighter-tag-editor",_ajax_nonce:urvanovSyntaxHighlighterTagEditorNonces.tagEditor,is_admin:w.is_admin},function(P){F=g('<div id="'+t.css+'"></div>');F.appendTo("body").hide();F.html(P);o.setOrigValues();q=F.find(t.submit_css);m=F.find(t.cancel_css);h=g(t.code_css);A=g("#urvanov-syntax-highlighter-te-clear");p=function(){var Q=A.is(":visible");if(h.val().length>0&&!Q){A.show();h.removeClass(w.selected)}else{if(h.val().length<=0){A.hide()}}};h.keyup(p);h.change(p);A.click(function(){h.val("");h.removeClass(w.selected);h.focus()});var N=g(t.url_css);var M=g(t.url_info_css);var O=UrvanovSyntaxHighlighterTagEditorSettings.extensions;x=function(){if(N.val().length>0&&!M.is(":visible")){M.show();N.removeClass(w.selected)}else{if(N.val().length<=0){M.hide()}}var S=UrvanovSyntaxHighlighterUtil.getExt(N.val());if(S){var T=O[S];var R=T?T:S;var Q=UrvanovSyntaxHighlighterTagEditorSettings.fallback_lang;g(t.lang_css+" option").each(function(){if(g(this).val()==R){Q=R}});g(t.lang_css).val(Q)}};N.keyup(x);N.change(x);var L=function(){var S=g(this);var Q=g(this).attr(w.orig_value);if(typeof Q=="undefined"){Q=""}var T=o.settingValue(S);UrvanovSyntaxHighlighterUtil.log(S.attr("id")+" value: "+T);var R=null;if(S.is("input[type=checkbox]")){R=S.next("span")}UrvanovSyntaxHighlighterUtil.log("   >>> "+S.attr("id")+" is "+Q+" = "+T);if(Q==T){S.removeClass(w.changed);if(R){R.removeClass(w.changed)}}else{S.addClass(w.changed);if(R){R.addClass(w.changed)}}o.settingValue(S,T)};g("."+w.setting+"[id]:not(."+w.special+")").each(function(){g(this).change(L);g(this).keyup(L)});s&&s()})};o.showDialog=function(L){var s=E;o.loadDialog(function(){g.colorbox(r);o._showDialog(L)})};o._showDialog=function(L){L=g.extend({insert:null,edit:null,show:null,hide:o.hide,select:null,editor_str:null,ed:null,node:null,input:null,output:null,br_html_block_after:"<p>&nbsp;</p>"},L);o.resetSettings();v=L.insert;k=L.edit;y=L.show;G=L.hide;C=L.select;J=L.input;z=L.output;n=L.editor_str;u=L.br_html_block_after;var M=L.node;var M=L.node;K=false;q.unbind();q.click(function(ac){o.submitButton();ac.preventDefault()});o.setSubmitText(t.submit_add);m.unbind();m.click(function(ac){o.hide();ac.preventDefault()});if(o.isUrvanovSyntaxHighlighter(M)){I=g(M);if(I.length!=0){l=I.attr("class");var S=new RegExp("\\b([A-Za-z-]+)"+t.attr_sep+"(\\S+)","gim");var s=S.execAll(l);l=g.trim(l.replace(S,""));var T={};for(var Y in s){var Q=s[Y][1];var U=s[Y][2];T[Q]=U}var ab=I.attr("title");if(ab){T.title=ab}var O=I.attr("data-url");if(O){T.url=O}if(typeof T.highlight!="undefined"){T.highlight="0"?"1":"0"}K=I.hasClass(a);T.inline=K?"1":"0";var Z=[];g(t.lang_css+" option").each(function(){var ac=g(this).val();if(ac){Z.push(ac)}});if(g.inArray(T.lang,Z)==-1){T.lang=t.fallback_lang}T=o.validate(T);for(var V in T){var R=g("#"+w.prefix+V+"."+w.setting);var U=T[V];o.settingValue(R,U);R.change();if(!R.hasClass(w.special)){R.addClass(w.changed);if(R.is("input[type=checkbox]")){highlight=R.next("span");highlight.addClass(w.changed)}}UrvanovSyntaxHighlighterUtil.log("loaded: "+V+":"+U)}j=true;o.setSubmitText(t.submit_edit);var X=I.html();if(J=="encode"){X=UrvanovSyntaxHighlighterUtil.encode_html(X)}else{if(J=="decode"){X=UrvanovSyntaxHighlighterUtil.decode_html(X)}}h.val(X)}else{UrvanovSyntaxHighlighterUtil.log("cannot load currNode of type pre")}}else{if(C){h.val(C)}j=false;o.setSubmitText(t.submit_add);I=null;l=""}var N=g("#"+t.inline_css);N.change(function(){K=g(this).is(":checked");var ac=g("."+t.inline_hide_css);var ag=g("."+t.inline_hide_only_css);var ae=[t.mark_css,t.range_css,t.title_css,t.url_css];for(var ad in ae){var af=g(ae[ad]);af.attr("disabled",K)}if(K){ac.hide();ag.hide();ac.closest("tr").hide();for(var ad in ae){var af=g(ae[ad]);af.addClass("urvanov-syntax-highlighter-disabled")}}else{ac.show();ag.show();ac.closest("tr").show();for(var ad in ae){var af=g(ae[ad]);af.removeClass("urvanov-syntax-highlighter-disabled")}}});N.change();var P=j?t.edit_text:t.add_text;g(t.dialog_title_css).html(P);if(y){y()}h.focus();p();x();if(B){clearInterval(B);D=0}var aa=g("#TB_window");aa.hide();var W=function(){aa.show();var ac=g(window).scrollTop();g(window).scrollTop(ac+10);g(window).scrollTop(ac-10)};B=setInterval(function(){if(typeof aa!="undefined"&&!aa.hasClass("urvanov-syntax-highlighter-te-ajax")){aa.addClass("urvanov-syntax-highlighter-te-ajax");clearInterval(B);W()}if(D>=100){clearInterval(B);W()}D++},40)};o.addUrvanovSyntaxHighlighter=function(){var L=g(t.url_css);if(L.val().length==0&&h.val().length==0){h.addClass(w.selected);h.focus();return false}h.removeClass(w.selected);var T=g("#"+t.inline_css);K=T.length!=0&&T.is(":checked");var N=br_after="";if(!j){if(!K){if(n=="html"){br_after=N=" \n"}else{br_after=u}}else{if(n=="html"){br_after=N=" "}else{br_after="&nbsp;"}}}var W=(K?"span":"pre");var P=N+"<"+W+" ";var R={};P+='class="';var s=new RegExp("\\b"+a+"\\b","gim");if(K){if(s.exec(l)==null){l+=" "+a+" "}}else{l=l.replace(s,"")}g("."+w.changed+"[id],."+w.changed+"["+t.data_value+"]").each(function(){var Y=g(this).attr("id");var X=g(this).attr(t.data_value);Y=i.removePrefixFromID(Y);R[Y]=X});R.lang=g(t.lang_css).val();var O=g(t.mark_css).val();if(O.length!=0&&!K){R.mark=O}var Q=g(t.range_css).val();if(Q.length!=0&&!K){R.range=Q}if(g(t.hl_css).is(":checked")){R.highlight="0"}R.decode="true";R=o.validate(R);for(var M in R){var V=R[M];UrvanovSyntaxHighlighterUtil.log("add "+M+":"+V);P+=M+t.attr_sep+V+" "}P+=l;P+='" ';if(!K){var U=g(t.title_css).val();if(U.length!=0){P+='title="'+U+'" '}var L=g(t.url_css).val();if(L.length!=0){P+='data-url="'+L+'" '}}var S=g(t.code_css).val();if(z=="encode"){S=UrvanovSyntaxHighlighterUtil.encode_html(S)}else{if(z=="decode"){S=UrvanovSyntaxHighlighterUtil.decode_html(S)}}S=typeof S!="undefined"?S:"";P+=">"+S+"</"+W+">"+br_after;if(j&&k){k(P)}else{if(v){v(P)}}return true};o.submitButton=function(){UrvanovSyntaxHighlighterUtil.log("submit");if(o.addUrvanovSyntaxHighlighter()!=false){o.hideDialog()}};o.hideDialog=function(){UrvanovSyntaxHighlighterUtil.log("hide");if(G){G()}};o.setOrigValues=function(){g("."+w.setting+"[id]").each(function(){var s=g(this);s.attr(w.orig_value,o.settingValue(s))})};o.resetSettings=function(){UrvanovSyntaxHighlighterUtil.log("reset");g("."+w.setting).each(function(){var s=g(this);o.settingValue(s,s.attr(w.orig_value));s.change()});h.val("")};o.settingValue=function(s,L){if(typeof L=="undefined"){L="";if(s.is("input[type=checkbox]")){L=s.is(":checked")?"true":"false"}else{L=s.val()}return L}else{if(s.is("input[type=checkbox]")){if(typeof L=="string"){if(L=="true"||L=="1"){L=true}else{if(L=="false"||L=="0"){L=false}}}s.prop("checked",L)}else{s.val(L)}s.attr(t.data_value,L)}};o.validate=function(N){var s=["range","mark"];for(var L in s){var M=s[L];if(typeof N[M]!="undefined"){N[M]=N[M].replace(/\s/g,"")}}return N};o.isUrvanovSyntaxHighlighter=function(s){return s!=null&&(s.nodeName=="PRE"||(s.nodeName=="SPAN"&&g(s).hasClass(a)))};o.elemValue=function(L){var s=null;if(L.is("input[type=checkbox]")){s=L.is(":checked")}else{s=L.val()}return s};o.setSubmitText=function(s){q.html(s)}}})(jQueryUrvanovSyntaxHighlighter,wp);