using selection-sets of program
preserving identifier escaping
without further secondary keys
receive results from function
at next application statement
corresponding fields of table
ignoring structure boundaries
keeping logical unit of work
with further secondary keys
ignoring conversion errors
with explicit enhancements
with implicit enhancements
with inactive enhancements
scan and check abap-source
with current switchstates
generate subroutine-pool
leave to list-processing
set left scroll-boundary
accepting duplicate keys
enhancement options into
exporting list to memory
no standard page heading
skipping byte-order mark
with precompiled headers
without selection-screen
syntax-check for program
call customer subscreen
corresponding fields of
environment time format
keeping directory entry
new list identification
end-enhancement-section
syntax-check for dynpro
call customer-function
fixed-point arithmetic
transporting no fields
using selection-screen
with list tokenization
multiply-corresponding
subtract-corresponding
call selection-screen
leave list-processing
set run time analyzer
and skip first screen
begin of tabbed block
during line-selection
in legacy binary mode
no database selection
non-unique sorted key
reduced functionality
replacement character
shared memory enabled
with windows linefeed
leave to transaction
accepting truncation
and return to screen
archiving parameters
begin of common part
daylight saving time
implementations from
include program from
on radiobutton group
on value-request for
renaming with suffix
via selection-screen
with byte-order mark
with free selections
with native linefeed
with selection-table
without spool dynpro
divide-corresponding
verification-message
at selection-screen
call transformation
exit from step-loop
adjacent duplicates
at cursor-selection
end of tabbed block
first occurrence of
in char-to-hex mode
in legacy text mode
on help-request for
option class-coding
preferred parameter
using selection-set
with non-unique key
with smart linefeed
enhancement-section
assign table field
convert time stamp
set extended check
set run time clock
as search patterns
dataset expiration
dynamic selections
end of common part
frame program from
from number format
in background task
in background unit
into sortable code
maximum width into
not at end of mode
radiobutton groups
replacement length
replacement offset
using no edit mask
with unix linefeed
move-corresponding
start-of-selection
protected section
at line-selection
describe distance
accepting padding
all other columns
defining database
deleting trailing
execute procedure
for all instances
in character mode
include structure
option syncpoints
radiobutton group
replacement count
respecting blanks
standard table of
starting new task
structure default
add-corresponding
end-of-definition
enhancement-point
assign component
call transaction
import directory
insert text-pool
set user-command
truncate dataset
all blob columns
all clob columns
as separate unit
begin of version
by kernel module
bypassing buffer
client specified
create protected
current position
deleting leading
enhancement into
field value into
init destination
main table field
matchcode object
no intervals off
no-extension off
open for package
replacement line
spool parameters
to number format
unicode enabling
with header line
end-of-selection
selection-screen
on chain-request
on value-request
scan abap-source
private section
at user-command
move percentage
raise exception
refresh control
set blank lines
set update task
suppress dialog
as person table
at exit-command
begin of screen
compression off
default program
directory entry
field selection
for all entries
from logfile id
hashed table of
inheriting from
initial line of
line value from
line value into
number of lines
number of pages
of current page
on exit-command
package section
respecting case
sorted table of
statements into
structures into
using edit mask
with type-pools
with unique key
authority-check
load-of-program
on help-request
generate dynpro
generate report
include methods
loop at screen
public section
delete dataset
describe field
describe table
export nametab
get time stamp
appendage type
begin of block
code page hint
code page into
compression on
create package
create private
default screen
display offset
end of version
extension type
from code page
get connection
global friends
in binary mode
in remote task
in update task
including gaps
internal table
list authority
lob handle for
maximum length
no end of line
non-unique key
obligatory off
on end of task
receive buffer
reference into
sap cover page
standard table
type tableview
visible length
whenever found
with table key
with test code
endenhancement
initialization
interface-pool
call subscreen
on chain-input
import nametab
select-options
call function
close dataset
create object
describe list
exit from sql
export dynpro
get parameter
get pf-status
get reference
insert report
leave program
modify screen
read textpool
rollback work
set hold data
set parameter
set pf-status
actual length
before output
before unwind
begin of line
binary search
create public
end of screen
final methods
for appending
from database
ignoring case
in background
include bound
keep in spool
keywords from
little endian
local friends
messages into
nesting level
option coding
option expand
overflow into
ref to object
shared buffer
shared memory
to first page
to lower case
to upper case
type tabstrip
valid between
with analysis
with comments
with includes
with linefeed
without trmac
class-methods
function-pool
print-control
import dynpro
field-symbols
close cursor
convert date
convert text
get property
get run time
leave screen
open dataset
read dataset
set language
set property
set titlebar
according to
archive mode
as subscreen
color yellow
display like
end of block
field format
for event of
from context
function key
hashed table
in byte mode
in text mode
include into
include type
initial line
initial size
keeping task
list dataset
match length
match offset
message into
no intervals
option class
options from
package size
program from
program type
separated by
sorted table
to code page
to last page
to last line
to sap spool
using screen
with pragmas
class-events
endinterface
field-groups
syntax-check
syntax-trace
call dialog
call method
call screen
commit work
create data
delete from
free memory
get dataset
modify line
open cursor
raise event
read report
set country
set dataset
set handler
after input
all methods
area handle
as checkbox
at position
backup into
binary mode
color black
color green
data buffer
data values
default key
end of file
end of line
endian into
for columns
for testing
frame entry
from screen
index table
left margin
levels into
line format
locator for
match count
next cursor
offset into
on rollback
primary key
ref to data
result into
search fkeq
search fkge
search gkeq
search gkge
send buffer
starting at
table field
tokens into
value check
break-point
concatenate
editor-call
end-of-file
end-of-page
endfunction
enhancement
new-section
top-of-page
load report
system-call
system-exit
get cursor
get locale
read table
set cursor
set locale
set margin
set screen
wait until
wait up to
all fields
and return
as listbox
big endian
color blue
color pink
connect to
cover page
cover text
for output
for select
for update
from table
in program
left outer
lower case
match line
object key
of program
reader for
risk level
section of
time stamp
to context
upper case
valid from
with frame
writer for
class-pool
endprovide
interfaces
on request
class-data
parameters
type-pools
at end of
call badi
read line
any table
as symbol
as window
code page
color red
edit mask
ending at
for field
for input
for lines
for table
line into
line page
list name
memory id
no dialog
no fields
on commit
on end of
print off
sorted by
text mode
time zone
to column
using key
with hold
with null
word into
endmethod
endmodule
endselect
infotypes
interface
log-point
translate
type-pool
constants
at first
exec sql
get badi
get time
and mark
and wait
begin of
for high
for node
for user
group by
if found
in group
in table
lines of
modif id
no flush
on block
order by
print on
range of
table of
with key
condense
endclass
function
multiply
new-line
new-page
position
subtract
transfer
endchain
on input
continue
endcatch
endwhile
controls
at last
loop at
get bit
set bit
as icon
as line
as text
for low
line of
of page
to line
to page
via job
aliases
collect
compute
endexec
endform
extract
include
maximum
message
methods
minimum
overlay
perform
program
provide
refresh
replace
reserve
summary
summing
process
cleanup
endcase
endloop
statics
at new
end of
ref to
append
assign
define
delete
demand
detail
divide
export
events
format
import
insert
method
modify
reject
report
scroll
search
select
submit
supply
unpack
update
window
assert
elseif
endtry
resume
return
fields
ranges
tables
up to
clear
class
endon
fetch
input
leave
raise
shift
split
uline
write
chain
catch
check
endat
endif
enddo
retry
while
local
types
find
form
free
hide
move
pack
skip
sort
case
else
exit
loop
stop
when
data
add
get
put
sum
try
at
do
if
