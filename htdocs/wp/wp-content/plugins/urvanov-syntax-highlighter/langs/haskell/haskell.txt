### HASKELL LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                Haskell
    VERSION             1.9.8

    COMMENT             (--.*?$)|({-[^\}]*})
    STRING              (?default)
    
    QUALIFIER:VARIABLE  (?<=import)\s+[^\s]+
    RESERVED            \b(?alt:reserved.txt)\b
    TYPE                \b(?alt:type.txt)\b
    
    RECORD:VARIABLE     \b\w+\b\s*(?=::)(?=[^{]*})
    ENTITY              \b\w+\b\s*(?=::)
    ARG:VARIABLE        (\b[\w\t ]+\b(?=\s*->))|((?<=->)\s*\b[\w\t ]+\b\s*$)
    CAPS:VARIABLE       (?-i)\b[A-Z]\w+\b(?i)
    IDENTIFIER          (?default)
    CONSTANT            (?default)
    OPERATOR            (?default)
    SYMBOL              (?default)
