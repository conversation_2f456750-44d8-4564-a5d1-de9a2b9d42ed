### COFFEESCRIPT LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                CoffeeScript
    VERSION             1.0

    COMMENT             (###[\s\S]*?###|#.*?$)
    STRING              (?default)|(%\w?\([^\)]*\))|(\`[^\`]*`)|(\<\<["'-]?\w+["']?)
    
    FUNCTION:KEYWORD    \b(?alt:function.txt)\b
    MODULE:KEYWORD      \b(?alt:module.txt)\b
    EXCEPTION:KEYWORD   \b(?alt:exception.txt)\b
    STATEMENT           \b(?alt:statement.txt)\b
    RESERVED            \b(?alt:reserved.txt)\b
    TYPE                \b(?alt:type.txt)\b
    MODIFIER            \b(?alt:modifier.txt)\b
    
    ENTITY              ((?-i)\b[A-Z_][A-Za-z_]*(?i))|(\w+):
    VARIABLE            ((@+)\w+)|this
    IDENTIFIER          (?default)
    CONSTANT            (?default)
    OPERATOR            (?default)
    SYMBOL              (?default)
