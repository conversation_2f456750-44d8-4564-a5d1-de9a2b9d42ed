parse-integer
complement
backquote
make-list
identity
defmacro
baktrace
evalhook
truncate
logcount
funcall
putprop
remprop
reverse
nsublis
maplist
symbolp
numberp
bignums
lognand
logorc2
logtest
logbitp
lambda
gensym
symbol
intern
caaaar
caaadr
caadar
caaddr
cadaar
cadadr
caddar
cadddr
cdaaar
cdaadr
cdadar
cdaddr
cddaar
cddadr
cdddar
cddddr
append
nthcdr
member
sublis
nsubst
remove
length
mapcar
mapcan
mapcon
rplaca
rplacd
delete
boundp
minusp
errset
random
logand
logior
logxor
lognot
logeqv
lognor
defun
princ
apply
quote
value
plist
caaar
caadr
cadar
caddr
cdaar
cdadr
cddar
cdddr
assoc
subst
nconc
listp
consp
zerop
plusp
evenp
equal
prog1
prog2
progn
print
write
eval
setq
setf
make
name
getf
aref
caar
cadr
cdar
cddr
cons
last
mapc
mapl
oddp
cond
case
prog
expt
sqrt
set
get
car
cdr
nth
eql
let
rem
min
max
abs
sin
cos
tan
exp
eq
l
