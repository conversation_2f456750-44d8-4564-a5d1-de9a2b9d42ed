__builtin_types_compatible_p
__builtin_return_address
constructor, destructor
__builtin_frame_address
no_instrument_function
__builtin_choose_expr
__builtin_constant_p
__builtin_apply_args
__builtin_prefetch
regparm, stkparm
__builtin_expect
__builtin_return
__builtin_apply
always_inline
__alignof__
format_arg
deprecated
interrupt
__FILE__
__LINE__
__func__
noreturn
noinline
__asm__
__align
__align
fortran
alignof
section
nonnull
nothrow
sprintf
sizeof
pascal
typeof
format
unused
malloc
printf
malloc
__asm
__asm
cdecl
const
alias
auto
near
huge
pure
used
asm
far
