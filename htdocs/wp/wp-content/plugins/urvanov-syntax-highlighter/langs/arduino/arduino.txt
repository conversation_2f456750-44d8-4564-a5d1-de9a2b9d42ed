### ARD<PERSON>NO LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                Arduino
    VERSION             1.0.0

    COMMENT             (?default)
    PREPROCESSOR		(?default)
    STRING              (?default)

    KEYWORD             \b(?alt:keywords.txt)\b
    
    STATEMENT           \b(?alt:statement.txt)\b
    RESERVED            (?default)|\b(?alt:reserved.txt)\b|\b(?alt:reserved2.txt)\b
    TYPE                \b(?alt:types.txt)\b
    MODIFIER            (?default)|\b(?alt:modifier.txt)\b
    
    ENTITY              \b(?alt:enity.txt)\b
    VARIABLE            (?default)
    CONSTANT            ((?-i)\b[A-Z_]*\b(?i))|((?default))
    IDENTIFIER          (?default)
    OPERATOR            (?default)
    SYMBOL              (?default)