waitforbuttonpress
selectmoveresize
uicontext Create
lightingangle
uicontextmenu
contourslice
reducevolume
cell2struct
partialpath
reducepatch
shrinkfaces
struct2cell
text Create
uiwait Used
cholupdate
ezcontourf
fieldnames
inferiorto
isonormals
isosurface
matlabroot
plotmatrix
profreport
str2double
streamline
superiorto
surf2patch
uisetcolor
camlookat
camtarget
colorcube
ezcontour
factorial
fileparts
ifftshift
inpolygon
inputname
intersect
rectangle
rrefmovie
spconvert
sprandsym
subvolume
uicontrol
uigetfile
uiputfile
uisetfont
varargout
vectorize
waterfall
wilkinson
workspace
bicgstab
bitshift
brighten
calendar
camdolly
camlight
camorbit
cart2pol
cart2sph
celldisp
cellplot
colorbar
colordef
colormap
computer
coneplot
contourc
contourf
contrast
convhull
copyfile
corrcoef
cplxpair
cumtrapz
cylinder
datetick
dbstatus
delaunay
dlmwrite
dragrect
errorbar
errordlg
fftshift
findfigs
fullfile
gammainc
getfield
gradient
griddata
hadamard
helpdesk
inputdlg
interpft
ipermute
ishandle
ismember
keyboard
lastwarn
legendre
lighting
linspace
logspace
material
matlabrc
menuedit
meshgrid
nchoosek
nextpow2
nonzeros
num2cell
pathtool
pbaspect
pol2cart
polyarea
polyvalm
printdlg
printopt
propedit
qrdelete
qrinsert
qrupdate
questdlg
randperm
rotate3d
scatter3
semilogx
semilogy
setfield
shiftdim
sortrows
specular
sph2cart
strmatch
subspace
surfnorm
tempname
texlabel
textread
textwrap
toeplitz
uiresume
varargin
wavwrite
whatsnew
wk1write
Bessely
addpath
auwrite
balance
besselh
besseli
besselj
besselk
betainc
bin2dec
blkdiag
builtin
bwcontr
camproj
camroll
camzoom
capture
cdf2rdf
cellfun
cellstr
cholinc
colperm
compass
complex
condeig
condest
contour
copyobj
cputime
cumprod
daspect
datenum
datestr
datevec
dbclear
dblquad
dbstack
deblank
dec2bin
dec2hex
diffuse
dlmread
drawnow
dsearch
ellipke
ezmeshc
ezplot3
ezpolar
ezsurfc
feather
filter2
findobj
findstr
flipdim
fprintf
frewind
gallery
gammaln
graymon
helpdlg
helpwin
hex2dec
hex2num
hsv2rgb
imfinfo
imwrite
ind2sub
int2str
interp1
interp2
interp3
interpn
invhilb
isocaps
lasterr
listdlg
loadobj
lookfor
mat2str
munlock
nargchk
nargout
newplot
normest
num2str
ode113,
ode15s,
ode23s,
ode23t,
ode23tb
odefile
openvar
pagedlg
permute
polyder
polyeig
polyfit
polyval
profile
quiver3
realmax
realmin
refresh
reshape
residue
rgb2hsv
rgbplot
rmfield
rsf2csf
saveobj
scatter
setdiff
shading
smooth3
soundsc
spalloc
spdiags
spinmap
spparms
sprandn
sprintf
squeeze
str2num
strcmpi
stream2
stream3
strings
strjust
strncmp
strvcat
sub2ind
subplot
surface
tempdir
trimesh
trisurf
tsearch
version
viewmtx
voronoi
waitbar
warndlg
warning
wavread
weekday
whitebg
wk1read
auread
autumn
betaln
bitand
bitcmp
bitget
bitmax
bitset
bitxor
campan
campos
clabel
colmmd
comet3
compan
copper
cumsum
dbcont
dbdown
dbquit
dbstep
dbstop
dbtype
deconv
delete
dialog
dmperm
docopt
double
ellipj
eomday
erfiny
evalin
expint
ezmesh
ezplot
ezsurf
factor
fclose
ferror
figure
filter
fliplr
flipud
format
fscanf
fwrite
ginput
hankel
hidden
imread
inline
legend
length
lin2mu
loglog
median
msgbox
mu2lin
nargin
ndgrid
ode45,
odeget
odeset
orient
pareto
pascal
pcolor
plotyy
primes
quiver
repmat
ribbon
rmpath
rotate
saveas
script
setxor
single
sparse
sphere
spline
spones
sprand
spring
sscanf
stairs
strcat
strcmp
strrep
strtok
struct
summer
symmmd
symrcm
symvar
uimenu
uint32
unique
unwrap
winter
xlabel
ylabel
zlabel
acosh
acoth
acsch
angle
asech
asinh
atan2
atanh
bar3h
bitor
camup
camva
caxis
class
clear
clock
close
comet
conv2
cross
dbmex
diary
erfcx
error
etime
evalc
feval
fgetl
fgets
fill3
floor
flops
fmins
fopen
fplot
fread
fseek
ftell
fzero
gamma
gmres
gtext
ifft2
ifftn
image
inmem
input
int16
int32
light
lines
log10
lower
lscov
luinc
magic
meshc
mkdir
mlock
ndims
nzmax
patch
pause
peaks
perms
plot3
polar
print
prism
randn
rbbox
rcond
reset
roots
rot90
round
schur
slice
sound
speye
spfun
sqrtm
stem3
surfc
surfl
title
trace
trapz
uint8
union
upper
which
zeros
acos
acot
acsc
airy
area
asec
asin
atan
axes
axis
bar3
barh
beta
bicg
bone
ceil
cell
char
chol
cond
conj
conv
cool
cosh
coth
csch
date
dbup
del2
diag
diff
disp
echo
edit
eigs
erfc
eval
expm
feof
fft2
fill
find
flag
fmin
full
funm
gcbo
gray
grid
gsvd
help
hess
hilb
hist
hold
home
ifft
imag
int8
line
load
log2
logm
mean
menu
mesh
more
nnls
norm
null
ones
open
orth
pack
path
pie3
pinv
plot
poly
pow2
prod
quad
quit
rand
rank
rats
real
rose
rref
save
sech
sign
sinh
size
sort
sqrt
stem
surf
svds
tanh
tril
triu
type
view
what
whos
xlim
ylim
zlim
zoom
Inf
NaN
abs
ans
bar
box
cat
cgs
cla
clc
clf
clg
cos
cot
cov
csc
det
dir
doc
eig
eps
erf
exp
eye
fft
fix
gca
gcd
gcf
gco
get
hdf
hot
hsv
inv
isa
jet
lcm
log
max
min
mod
nnz
now
pcg
pie
pwd
qmr
rat
rem
sec
set
shg
sin
std
sum
svd
tan
tic
toc
var
ver
web
who
cd
ls
lu
pi
qr
qz
i
j
