# Keywords
abstract
and
array
as
asm
begin
break
case
cdecl
class
const
constructor
contains
continue
deprecated
destructor
div
do
downto
else
end
ensure
except
exit
export
exports
external
final
finalization
finally
for
forward
function
helper
if
implementation
implements
implies
in
inherited
initialization
inline
interface
is
lambda
lazy
library
message
method
mod
new
nil
not
object
of
old
on
operator
or
overload
override
pascal
partial
private
procedure
program
property
protected
public
published
raise
record
register
reintroduce
repeat
require
resourcestring
sar
sealed
set
shl
shr
static
step
then
to
try
type
unit
until
uses
var
virtual
while
xor