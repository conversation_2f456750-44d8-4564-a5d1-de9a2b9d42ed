### CSS LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                CSS
    VERSION             1.9.1

    COMMENT             (/\*.*?\*/)
    STRING              (?default)
    NOTATION            \@[\w-]+[^;\{]*;?

    # For the <style> tag
    ATT_STR:STRING      (((?<!\\)".*?(?<!\\)")|((?<!\\)'.*?(?<!\\)'))
    TAG     			(</?\s*[^<\s>]+\s*>?)|(\s*>)
    ATTR:ENTITY         [\w-]+(?=\s*=\s*["'])

    SELECTOR:KEYWORD    [^\s\;\{\}][^\;\{\}]*(?=\{)
    PROPERTY:ENTITY     [\w-]+(?=\s*:)
    IMP:CONSTANT        !important
    VALUE:IDENTIFIER    [^\s\{\}\;\:\!\(\)]+
    SYMBOL              (?default)
