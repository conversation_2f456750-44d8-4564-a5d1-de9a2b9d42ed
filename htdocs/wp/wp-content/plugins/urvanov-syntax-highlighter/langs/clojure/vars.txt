*'
*1
*2
*3
*agent*
*allow-unresolved-vars*
*assert*
*clojure-version*
*command-line-args*
*compile-files*
*compile-path*
*compiler-options*
*data-readers*
*default-data-reader-fn*
*e
*err*
*file*
*flush-on-newline*
*fn-loader*
*in*
*math-context*
*ns*
*out*
*print-dup*
*print-length*
*print-level*
*print-meta*
*print-readably*
*read-eval*
*source-path*
*unchecked-math*
*use-context-classloader*
*verbose-defrecords*
*warn-on-reflection*
