returns null on null input
current_timestamp
characteristics
current_catalog
current_schema
localtimestamp
authorization
configuration
xmlattributes
concurrently
current_date
current_role
current_time
current_user
nocreaterole
nocreateuser
serializable
session_user
xmlserialize
constraints
nulls first
constraints
insensitive
lancompiler
nosuperuser
uncommitted
unencrypted
conversion
privileges
tablespace
constraint
references
nulls last
assignment
asymmetric
connection
constraint
conversion
createrole
createuser
deferrable
delimiters
dictionary
lc_collate
nocreatedb
privileges
procedural
references
repeatable
standalone
statistics
tablespace
whitespace
xmlelement
aggregate
collation
extension
including
no action
immutable
aggregate
assertion
committed
delimiter
encrypted
excluding
exclusive
following
immediate
immutable
including
increment
initially
isolation
localtime
noinherit
partition
preceding
precision
procedure
statement
superuser
symmetric
temporary
unbounded
validator
xmlconcat
xmlforest
database
function
language
operator
sequence
not null
defaults
inherits
set null
restrict
volatile
distinct
absolute
backward
cascaded
continue
createdb
database
defaults
deferred
distinct
document
encoding
external
function
identity
implicit
inherits
language
lc_ctype
location
maxvalue
minvalue
national
operator
overlaps
password
position
prepared
preserve
reassign
relative
restrict
security
sequence
template
trailing
variadic
volatile
xmlparse
default
foreign
wrapper
trigger
mapping
primary
cascade
returns
plpgsql
extract
analyse
between
cascade
catalog
collate
content
current
default
definer
disable
extract
foreign
forward
granted
handler
indexes
inherit
invoker
leading
mapping
natural
nologin
nothing
notnull
numeric
options
overlay
partial
placing
primary
recheck
release
replica
restart
returns
session
similar
storage
trigger
trusted
unknown
verbose
version
without
wrapper
xmlroot
domain
object
family
schema
server
unique
stable
strict
offset
access
action
always
called
column
cursor
domain
double
enable
escape
family
freeze
global
header
isnull
minute
nowait
object
offset
option
parser
rename
return
revoke
schema
scroll
search
second
server
simple
stable
stdout
strict
system
unique
window
table
group
index
large
class
limit
check
abort
admin
cache
chain
check
class
cross
cycle
FALSE
fetch
first
force
group
ilike
index
inner
inout
input
large
level
limit
local
login
match
month
names
nulls
order
outer
owned
owner
plans
prior
quote
range
reset
right
setof
share
start
stdin
strip
sysid
table
treat
until
valid
value
while
write
xmlpi
data
role
type
user
null
like
only
desc
both
cast
cost
data
desc
each
full
hold
hour
last
left
like
mode
name
next
none
null
oids
only
over
read
role
rows
show
temp
TRUE
type
user
view
work
year
zone
key
add
sql
asc
new
old
add
and
asc
csv
day
dec
key
new
not
off
old
out
row
yes
as
on
to
as
at
by
is
no
of
on
or
to
