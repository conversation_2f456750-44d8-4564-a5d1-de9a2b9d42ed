getprotobynumber
getprotobyname
gethostbyaddr
gethostbyname
getservbyname
getservbyport
getnetbyaddr
getnetbyname
endprotoent
getpeername
getpriority
getprotoent
getsockname
setpriority
setprotoent
endhostent
endservent
gethostent
getservent
getsockopt
sethostent
setservent
setsockopt
socketpair
endnetent
getnetent
localtime
prototype
quotemeta
rewinddir
setnetent
wantarray
closedir
dbmclose
endgrent
endpwent
formline
getgrent
getgrgid
getgrnam
getlogin
getpwent
getpwnam
getpwuid
readline
readlink
readpipe
setgrent
setpwent
shmwrite
shutdown
syswrite
truncate
binmode
connect
dbmopen
defined
getpgrp
getppid
lcfirst
opendir
package
readdir
require
reverse
seekdir
setpgrp
shmread
sprintf
symlink
syscall
sysopen
sysread
sysseek
telldir
ucfirst
unshift
waitpid
accept
caller
chroot
delete
exists
fileno
format
gmtime
import
length
listen
msgctl
msgget
msgrcv
msgsnd
printf
rename
return
rindex
scalar
select
semctl
semget
shmctl
shmget
socket
splice
substr
system
unlink
unpack
values
alarm
atan2
bless
chdir
chmod
chomp
chown
close
crypt
fcntl
flock
index
ioctl
local
lstat
mkdir
print
rmdir
semop
shift
sleep
split
srand
study
times
umask
undef
untie
utime
write
bind
chop
dump
each
eval
exec
exit
fork
getc
glob
goto
grep
join
keys
kill
link
open
pack
pipe
push
rand
read
recv
seek
send
sort
sqrt
stat
tell
tied
time
wait
warn
abs
chr
cos
die
eof
exp
hex
int
log
map
oct
ord
pop
pos
ref
sin
tie
vec
use
sub
new
lc
no
qq
qr
qw
qx
tr
uc
m
q
s
y