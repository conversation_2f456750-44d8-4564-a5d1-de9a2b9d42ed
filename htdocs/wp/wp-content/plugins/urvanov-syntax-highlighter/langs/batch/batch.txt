### MS DOS BATCH SCRIPT ###
### Author: <PERSON>.<EMAIL> ###
 
#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION
 
    NAME                MS DOS
    VERSION             1.1
 
    STRING              (?default)|(?<=\becho\b).*?$
        
    COMMENT             ^::.*?$|^rem\s(.*?)$
    
    LABEL:FADED         :[a-zA-Z_\-][a-zA-Z0-9_\-\.]+
    
    KEYWORD             @?\b(?alt:keywords.txt)\b
    
    RESERVED            @?\b(?alt:builtins.txt)\b
    COMMAND:RESERVED    ^@?\b[a-zA-Z][a-zA-Z0-9_\-\.]*\b
    
    VARIABLE            %?%[0-9]+|%[^%\s]+%
    
    PARAMETER:ENTITY    \s-[a-zA-Z][a-zA-Z0-9]*
    
    OPERATOR            (?default)
    SYMBOL              (?default)
