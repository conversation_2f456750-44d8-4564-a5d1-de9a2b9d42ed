### PHP LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                        PHP
    VERSION                     1.9.4
  
    COMMENT                     (?default)|(\#.*?$)
    STRING                      (?default)|(<<<EOT.*?^EOT)
    
    TAG                         <\?php\b|<\?=|<\?|\?>
    CONSTRUCT:KEYWORD           \b(?alt:construct.txt)\b
    STATEMENT                   (?default)
    RESERVED                    (?default)
    TYPE                        (?default)
    MODIFIER                    (?default)
    COMPILE:KEYWORD             \b(?alt:compile.txt)\b
    
    RES_CONST:ENTITY            \b(?alt:reserved.txt)\b
    ENTITY                      (?default)|\b[a-z_]\w*::
    VARIABLE                    \$[a-z_]\w*\b
    OTHER_ID:IDENTIFIER         \b[a-z_]\w*\b\s*(?=\([^\)]*\))
    CONSTANT                    ((?-i)\b[A-Z_]*\b(?i))|((?default))
    IDENTIFIER                  (?default)
    OPERATOR                    (?default)
    SYMBOL                      (?default)
