### DEFAULT LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                Default
    VERSION             1.8.1

    COMMENT             (/\*.*?\*/)|(//.*?$)
    PREPROCESSOR		(#.*?$)
    STRING              ((?<![^\\]\\)".*?(?<![^\\]\\)")|((?<![^\\]\\)'.*?(?<![^\\]\\)')
    
    STATEMENT           \b(?alt:statement.txt)\b
    RESERVED            \b(?<![:\.])(?alt:reserved.txt)\b
    					# \b(?<![:\.])(?alt:reserved.txt)(?![:\.])\b
    TYPE                \b(?alt:type.txt)\b
    MODIFIER            \b(?alt:modifier.txt)\b

						# func() | func { | (Type) Var
    ENTITY              (\b[a-z_]\w*\b(?=\s*\([^\)]*\)))|((?<!\.)(\b[a-z_]\w*\b)(?=[^}=|,.:;"'\)]*{))|(\b[a-z_]\w+\b\s+(?=\b[a-z_]\w+\b))
    # C variants only: String *
    POINTER_TYPE:ENTITY (\b[a-z_]\w*\s*\*)
    
    VARIABLE            [A-Za-z_]\w*(?=\s*(?alt:operator.txt)|(?alt:symbol.txt)|$)
    IDENTIFIER          \b[A-Za-z_]\w*\b
    CONSTANT            (?<!\w)[0-9][\.\w]*
    OPERATOR            (?alt:operator.txt)
    SYMBOL              &[^;]+;|(?alt:symbol.txt)
    
    # |[^\w\s\d'"] in SYMBOL causes it to crash
    
