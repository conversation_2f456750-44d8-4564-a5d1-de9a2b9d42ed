# comparison
=
<>
>=
<=
Новый
Для
Каждого
Из
Цикл
Перем
Экспорт
Функция
Процедура
ИначеЕсли
Если
Или
Не
Ложь
КонецПроцедуры
КонецФункции
Истина
И
КонецЕсли
Возврат
Тогда
Попытка
Исключение
КонецПопытки
КонецЦикла
Неопределено
НОВЫЙ
ДЛЯ
КАЖДОГО
ИЗ
ЦИКЛ
ПЕРЕМ
ЭКСПОРТ
ФУНКЦИЯ
ПРОЦЕДУРА
ИНАЧЕЕСЛИ
ЕСЛИ
ИЛИ
НЕ
ЛОЖЬ
КОНЕЦПРОЦЕДУРЫ
КОНЕЦФУНКЦИИ
ИСТИНА
И
КОНЕЦЕСЛИ
ВОЗВРАТ
ТОГДА
ПОПЫТКА
ИСКЛЮЧЕНИЕ
КОНЕЦПОПЫТКИ
КОНЕЦЦИКЛА
НЕОПРЕДЕЛЕНО
новый
для
каждого
из
цикл
перем
функция
процедура
иначеесли
если
или
не
ложь
конецпроцедуры
конецфункции
истина
и
конецесли
возврат
тогда
попытка
исключение
конецпопытки
конеццикла
неопределено