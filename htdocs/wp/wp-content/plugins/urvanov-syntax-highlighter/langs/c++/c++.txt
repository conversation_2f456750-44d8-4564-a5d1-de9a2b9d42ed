### C++ LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                C++
    VERSION             1.8.1

    COMMENT             (?default)
    PREPROCESSOR		(?default)
    STRING              (?default)
    
    STATEMENT           (?default)|\b(?alt:statement.txt)\b
    RESERVED            (?default)|\b(?alt:reserved.txt)\b
    TYPE                (?default)|\b(?alt:type.txt)\b
    MODIFIER            (?default)|\b(?alt:modifier.txt)\b
    
    ENTITY              (?default)
    # TODO: the use of priorities would be suitable here, last check for &*** vars might match entity
    VARIABLE            (?default)|(?default:identifier)(?=::)|\b(?<=\-\>)\s*[A-Za-z_]\w*|&(?default:identifier)\s*(?!\()
    IDENTIFIER          (?default)
    CONSTANT            (?default)
    OPERATOR            (?default)
    SYMBOL              (?default)
