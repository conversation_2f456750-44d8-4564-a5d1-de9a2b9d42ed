__multiple_inheritance
__virtual_inheritance
__single_inheritance
reinterpret_cast
dynamic_cast
__identifier
static_cast
__unaligned
__alignof__
const_cast
__delegate
deprecated
__fastcall
__alignof
dllexport
dllimport
__stdcall
alignas 
explicit
noexcept
typename
__assume
delegate
__except
initonly
ifstream
ofstream
noreturn
novtable
safecast
__sealed
__unhook
__uuidof
alignof
__based
__event
nothrow
__raise
__super
__align
__asm__
sizeof
export
typeid
__hook
__noop
sealed
compl
using
__asm
__box
event
naked
__asm
cout
uuid
asm
