MASTER_SSL_VERIFY_SERVER_CERT
SQL_CALC_FOUND_ROWS
MINUTE_MICROSECOND
NO_WRITE_TO_BINLOG
SECOND_MICROSECOND
CURRENT_TIMESTAMP
HOUR_MICROSECOND
SQL_SMALL_RESULT
DAY_MICROSECOND
IO_BEFORE_GTIDS
IO_AFTER_GTIDS
LOCALTIMESTAMP
SQL_BIG_RESULT
DETERMINISTIC
HIGH_PRIORITY
MINUTE_SECOND
STRAIGHT_JOIN
UTC_TIMESTAMP
CURRENT_DATE
CURRENT_TIME
CURRENT_USER
LOW_PRIORITY
SQLEXCEPTION
VARCHARACTER
DISTINCTROW
HOUR_MINUTE
HOUR_SECOND
INSENSITIVE
MASTER_BIND
NONBLOCKING
ACCESSIBLE
ASENSITIVE
CONSTRAINT
DAY_MINUTE
DAY_SECOND
OPTIONALLY
READ_WRITE
REFERENCES
SQLWARNING
TERMINATED
YEAR_MONTH
CHARACTER
CONDITION
DATABASES
LOCALTIME
MIDDLEINT
PARTITION
PRECISION
PROCEDURE
SENSITIVE
SEPARATOR
VARBINARY
CONTINUE
DATABASE
DAY_HOUR
DESCRIBE
DISTINCT
ENCLOSED
FULLTEXT
INTERVAL
MAXVALUE
MODIFIES
OPTIMIZE
RESIGNAL
RESTRICT
SPECIFIC
SQLSTATE
STARTING
TRAILING
UNSIGNED
UTC_DATE
UTC_TIME
ZEROFILL
ANALYZE
BETWEEN
CASCADE
COLLATE
CONVERT
DECLARE
DEFAULT
DELAYED
ESCAPED
EXPLAIN
FOREIGN
ITERATE
LEADING
NATURAL
NUMERIC
OUTFILE
PRIMARY
RELEASE
REPLACE
REQUIRE
SCHEMAS
SPATIAL
TRIGGER
VARYING
BEFORE
CHANGE
COLUMN
CREATE
CURSOR
DELETE
ELSEIF
EXISTS
FLOAT4
FLOAT8
HAVING
IGNORE
INFILE
INSERT
LINEAR
OPTION
REGEXP
RENAME
REPEAT
RETURN
REVOKE
SCHEMA
SELECT
SIGNAL
UNIQUE
UNLOCK
UPDATE
VALUES
ALTER
CHECK
CROSS
FETCH
FLOAT
FORCE
GRANT
GROUP
INDEX
INNER
INOUT
LEAVE
LIMIT
LINES
MATCH
ORDER
OUTER
PURGE
RANGE
READS
RIGHT
RLIKE
TABLE
UNION
USAGE
USING
WHERE
WHILE
WRITE
FALSE
BOTH
CALL
CASE
DESC
DROP
DUAL
EACH
ELSE
EXIT
FROM
INT1
INT2
INT3
INT4
INT8
INTO
JOIN
KEYS
KILL
LEFT
LIKE
LOAD
LOCK
LONG
LOOP
NULL
READ
REAL
SHOW
THEN
UNDO
WHEN
WITH
TRUE
ADD
ALL
AND
ASC
DEC
DIV
FOR
GET
KEY
MOD
NOT
OUT
SET
SQL
SSL
USE
XOR
AS
BY
IF
IN
IS
ON
OR
TO