MAX_CONNECTIONS_PER_HOUR
SQL_LOW_PRIORITY_UPDATES
MASTER_HEARTBEAT_PERIOD
SQL_SLAVE_SKIP_COUNTER
SQL_QUOTE_SHOW_CREATE
MASTER_CONNECT_RETRY
MAX_QUERIES_PER_HOUR
MAX_UPDATES_PER_HOUR
MAX_USER_CONNECTIONS
GEMINI_SPIN_RETRIES
SQL_CALC_FOUND_ROWS
CURRENT_TIMESTAMP
IGNORE_SERVER_IDS
SQL_BUFFER_RESULT
SQL_MAX_JOIN_SIZE
SQL_AUTO_IS_NULL
SQL_SAFE_UPDATES
SQL_SELECT_LIMIT
SQL_SMALL_RESULT
DELAY_KEY_WRITE
MASTER_LOG_FILE
MASTER_PASSWORD
SQL_BIG_SELECTS
AUTO_INCREMENT
AVG_ROW_LENGTH
LAST_INSERT_ID
MASTER_LOG_POS
RAID_CHUNKSIZE
SQL_BIG_RESULT
SQL_BIG_TABLES
SQL_LOG_UPDATE
DETERMINISTIC
HIGH_PRIORITY
INSERT_METHOD
MINUTE_SECOND
PAGE_CHECKSUM
STRAIGHT_JOIN
TRANSACTIONAL
LOW_PRIORITY
SERIALIZABLE
SQL_NO_CACHE
SQL_WARNINGS
DISTINCTROW
HOUR_MINUTE
HOUR_SECOND
MASTER_HOST
MASTER_PORT
MASTER_USER
PROCESSLIST
RAID_CHUNKS
REPLICATION
SQL_LOG_BIN
SQL_LOG_OFF
UNCOMMITTED
MEDIUMBLOB
MEDIUMTEXT
ACCESSIBLE
AUTOCOMMIT
BERKELEYDB
COMPRESSED
CONCURRENT
CONSTRAINT
DAY_MINUTE
DAY_SECOND
IDENTIFIED
MRG_MYISAM
NDBCLUSTER
OPTIONALLY
PARTITIONS
PRIVILEGES
READ_WRITE
REFERENCES
REPEATABLE
ROW_FORMAT
TERMINATED
YEAR_MONTH
MEDIUMINT
TIMESTAMP
VARBINARY
AGGREGATE
ALGORITHM
BLACKHOLE
COLLATION
COMMITTED
DATABASES
DELIMITER
DUPLICATE
FEDERATED
INSERT_ID
ISOLATION
PACK_KEYS
PARTITION
PRECISION
PROCEDURE
RAID_TYPE
READ_ONLY
SEPARATOR
SQL_CACHE
TEMPORARY
UNDEFINED
VARIABLES
DATETIME
LONGBLOB
LONGTEXT
SMALLINT
TINYBLOB
TINYTEXT
CHECKSUM
CONTAINS
DATABASE
DAY_HOUR
DESCRIBE
DISTINCT
DUMPFILE
ENCLOSED
EXTENDED
FULLTEXT
FUNCTION
INNOBASE
INTERVAL
MAXVALUE
MAX_ROWS
MIN_ROWS
MRG_ISAM
NATIONAL
OPTIMIZE
PASSWORD
RESIGNAL
RESTRICT
ROLLBACK
SECURITY
SHUTDOWN
STARTING
TRAILING
TRUNCATE
UNSIGNED
ZEROFILL
BOOLEAN
DECIMAL
INTEGER
NUMERIC
TINYINT
VARCHAR
AGAINST
ANALYSE
ANALYZE
ARCHIVE
CASCADE
CHANGED
CHARSET
COLUMNS
COMMENT
CONVERT
DECLARE
DEFAULT
DEFINER
DELAYED
DYNAMIC
ENGINES
ESCAPED
EXAMPLE
EXECUTE
EXPLAIN
FOREIGN
GENERAL
INDEXES
INVOKER
LEADING
NATURAL
OUTFILE
PARTIAL
PRIMARY
PROCESS
REPLACE
RESTORE
RETURNS
SESSION
STORAGE
STRIPED
TRIGGER
UNICODE
VARYING
BIGINT
BINARY
DOUBLE
ACTION
BACKUP
BINLOG
CHANGE
COLUMN
COMMIT
CREATE
DELETE
ENGINE
ESCAPE
EVENTS
EXISTS
FIELDS
GEMINI
GLOBAL
GRANTS
HAVING
IGNORE
INFILE
INNODB
INSERT
LINEAR
MEDIUM
MEMORY
MINUTE
MODIFY
MYISAM
OPTION
RELOAD
RENAME
REPAIR
RETURN
REVOKE
SECOND
SELECT
SIGNAL
SONAME
STATUS
STRING
TABLES
UNIQUE
UNLOCK
UPDATE
VALUES
FLOAT
AFTER
ALTER
ASCII
BEGIN
CHECK
CROSS
FALSE
FIRST
FIXED
FLUSH
FORCE
GRANT
GROUP
HOSTS
INDEX
INNER
LIMIT
LINES
LOCAL
LOCKS
MARIA
MATCH
MERGE
MONTH
NAMES
ORDER
OUTER
PURGE
QUICK
RAID0
RANGE
RESET
RIGHT
SHARE
SLAVE
START
SUPER
TABLE
TYPES
UNION
USAGE
USING
WHERE
WRITE
BLOB
BOOL
CHAR
DATE
ENUM
TEXT
TIME
YEAR
BOTH
CALL
DESC
DROP
EACH
ELSE
FAST
FILE
FROM
FULL
HEAP
HOUR
INTO
ISAM
JOIN
KEYS
KILL
LEFT
LOAD
LOCK
LOGS
MODE
OPEN
PAGE
READ
ROWS
SHOW
SLOW
STOP
THEN
TRUE
TYPE
VIEW
WHEN
WITH
WORK
BIT
INT
SET
ADD
ALL
ASC
BDB
CSV
DAY
END
FOR
KEY
NDB
ROW
SET
SQL
USE
AS
BY
DO
IF
NO
ON
TO
