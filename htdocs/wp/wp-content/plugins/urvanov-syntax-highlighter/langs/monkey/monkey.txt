### MONKEY LANGUAGE ###

#   ELEMENT_NAME [optional-css-class] REGULAR_EXPRESSION

    NAME                Monkey
    VERSION             1.0

    COMMENT             (#rem.*?#end)|('.*?$)
    STRING              ((?<!\\)".*?(?<!\\)")
    
    NOTATION            \summary:[\w-]+
    STATEMENT           (?default)|\b(?alt:statement.txt)\b
    RESERVED            (?default)|\b(?alt:reserved.txt)\b
    TYPE                (?default)
    MODIFIER            (?default)|\b(?alt:modifier.txt)\b
    
    ENTITY              (?default)
    VARIABLE            (?default)
    GENERIC:ENTITY      <\w+>
    IDENTIFIER          (?default)
    CONSTANT            (?default)
    OPERATOR            (?default)|\b(?alt:operator.txt)\b
    SYMBOL              (?default)
