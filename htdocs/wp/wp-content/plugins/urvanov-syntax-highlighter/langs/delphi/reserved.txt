# Functions
Abs
Addr
AnsiCompareStr
AnsiCompareText
AnsiContainsStr
AnsiContainsText
AnsiEndsStr
AnsiIndexStr
AnsiLeftStr
AnsiLowerCase
AnsiMatchStr
AnsiMidStr
AnsiPos
AnsiReplaceStr
AnsiReverseString
AnsiRightStr
AnsiStartsStr
AnsiUpperCase
ArcCos
ArcSin
ArcTan
Assigned
BeginThread
Bounds
CelsiusToFahrenheit
ChangeFileExt
Convert
CompareStr
CompareText
CompareValue
Concat
Convert
Copy
Cos
CreateDir
CurrToStr
CurrToStrF
Date
DateTimeToFileDate
DateTimeToStr
DateToStr
DayOfTheMonth
DayOfTheWeek
DayOfTheYear
DayOfWeek
DaysBetween
DaysInAMonth
DaysInAYear
DaySpan
DegToRad
DeleteFile
DirectoryExists
DiskFree
DiskSize
DupeString
EncodeDate
EncodeDateTime
EncodeTime
EndOfADay
EndOfAMonth
Eof
Eoln
Exp
ExtractFileDir
ExtractFileDrive
ExtractFileExt
ExtractFileName
ExtractFilePath
FahrenheitToCelsius
FileAge
FileDateToDateTime
FileExists
FileGetAttr
FilePos
FileSearch
FileSetAttr
FileSetDate
FileSize
FindClose
FindCmdLineSwitch
FindFirst
FindNext
FloatToStr
FloatToStrF
ForceDirectories
Format
FormatCurr
FormatDateTime
FormatFloat
Frac
GetCurrentDir
GetLastError
GetMem
Hi
High
IncDay
IncHour
IncMillisecond
IncMinute
IncMonth
IncSecond
IncYear
InputBox
InputQuery
Int
IntToHex
IntToStr
IOResult
IsInfinite
IsLeapYear
IsMultiThread
IsNaN
LastDelimiter
Length
Ln
Lo
Log10
Low
LowerCase
Max
Mean
MessageDlg
MessageDlgPos
Min
MonthOfTheYear
Now
Odd
Ord
ParamCount
ParamStr
Pi
Point
PointsEqual
Pos
Pred
Printer
PromptForFileName
PtInRect
RadToDeg
Random
RandomRange
RecodeDate
RecodeTime
Rect
RemoveDir
RenameFile
Round
SeekEof
SeekEoln
SelectDirectory
SetCurrentDir
Sin
SizeOf
Slice
Sqr
Sqrt
StringOfChar
StringReplace
StringToWideChar
StrScan
StrToCurr
StrToDate
StrToDateTime
StrToFloat
StrToInt
StrToInt64
StrToInt64Def
StrToIntDef
StrToTime
StuffString
Succ
Sum
Tan
Time
TimeToStr
Tomorrow
Trim
TrimLeft
TrimRight
Trunc
UpCase
UpperCase
VarType
WideCharToString
WrapText
Yesterday







# working on these
absolute
abstract
and
Application
as
asm
assembler
at
automated
case
cdecl
class
comp
const
constructor
contains
default
deprecated
destructor
dispid
dispinterface
div
do
downto
dynamic
else
end
except
Exception
export
exports
external
false
far
file
final
finalization
finally
for
forward
function
goto
if
implementation
implements
in
index
inherited
initialization
inline
interface
is
label
library
local
message
mod
name
near
nil
null
nodefault
not
of
on
or
out
overload
override
package
packed
pascal
platform
private
procedure
program
property
protected
public
published
raise
read
readonly
record
register
reintroduce
remove
repeat
requires
resident
resourcestring
safecall
Self
set
shl
shr
static
stdcall
stored
strictprivate
strictprotected
then
threadvar
to
true
try
type
unit
unsafe
until
uses
var
varargs
virtual
while
with
write
writeonly
xor
