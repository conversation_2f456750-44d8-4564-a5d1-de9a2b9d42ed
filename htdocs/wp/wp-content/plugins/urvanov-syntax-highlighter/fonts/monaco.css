@font-face {
    font-family: 'MonacoRegular';
    src: url('monaco/monaco-webfont.eot');
    src: url('monaco/monaco-webfont.eot?#iefix') format('embedded-opentype'),
         url('monaco/monaco-webfont.woff') format('woff'),
         url('monaco/monaco-webfont.ttf') format('truetype'),
         url('monaco/monaco-webfont.svg#MonacoRegular') format('svg');
    font-weight: normal;
    font-style: normal;
}

.urvanov-syntax-highlighter-font-monaco * {
    font-family: Monaco, 'MonacoRegular', 'Courier New', monospace !important;
}