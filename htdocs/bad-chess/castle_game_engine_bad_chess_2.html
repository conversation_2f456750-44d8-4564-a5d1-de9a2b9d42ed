<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.23">
<meta name="author" content="<PERSON><PERSON><PERSON>">
<title>The bad way to play chess: 3D physics fun using Castle Game Engine (Part 2)</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ul.square{list-style-type:square}
ul.circle ul:not([class]),ul.disc ul:not([class]),ul.square ul:not([class]){list-style:inherit}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child{border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:first-child,.sidebarblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child,.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active,#footnotes .footnote a:first-of-type:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,td.hdlist1,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
<style>
/*! Stylesheet for CodeRay to loosely match GitHub themes | MIT License */
pre.CodeRay{background:#f7f7f8}
.CodeRay .line-numbers{border-right:1px solid;opacity:.35;padding:0 .5em 0 0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.CodeRay span.line-numbers{display:inline-block;margin-right:.75em}
.CodeRay .line-numbers strong{color:#000}
table.CodeRay{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.CodeRay td{vertical-align:top;line-height:inherit}
table.CodeRay td.line-numbers{text-align:right}
table.CodeRay td.code{padding:0 0 0 .75em}
.CodeRay .debug{color:#fff!important;background:navy!important}
.CodeRay .annotation{color:#007}
.CodeRay .attribute-name{color:navy}
.CodeRay .attribute-value{color:#700}
.CodeRay .binary{color:#509}
.CodeRay .comment{color:#998;font-style:italic}
.CodeRay .char{color:#04d}
.CodeRay .char .content{color:#04d}
.CodeRay .char .delimiter{color:#039}
.CodeRay .class{color:#458;font-weight:bold}
.CodeRay .complex{color:#a08}
.CodeRay .constant,.CodeRay .predefined-constant{color:teal}
.CodeRay .color{color:#099}
.CodeRay .class-variable{color:#369}
.CodeRay .decorator{color:#b0b}
.CodeRay .definition{color:#099}
.CodeRay .delimiter{color:#000}
.CodeRay .doc{color:#970}
.CodeRay .doctype{color:#34b}
.CodeRay .doc-string{color:#d42}
.CodeRay .escape{color:#666}
.CodeRay .entity{color:#800}
.CodeRay .error{color:#808}
.CodeRay .exception{color:inherit}
.CodeRay .filename{color:#099}
.CodeRay .function{color:#900;font-weight:bold}
.CodeRay .global-variable{color:teal}
.CodeRay .hex{color:#058}
.CodeRay .integer,.CodeRay .float{color:#099}
.CodeRay .include{color:#555}
.CodeRay .inline{color:#000}
.CodeRay .inline .inline{background:#ccc}
.CodeRay .inline .inline .inline{background:#bbb}
.CodeRay .inline .inline-delimiter{color:#d14}
.CodeRay .inline-delimiter{color:#d14}
.CodeRay .important{color:#555;font-weight:bold}
.CodeRay .interpreted{color:#b2b}
.CodeRay .instance-variable{color:teal}
.CodeRay .label{color:#970}
.CodeRay .local-variable{color:#963}
.CodeRay .octal{color:#40e}
.CodeRay .predefined{color:#369}
.CodeRay .preprocessor{color:#579}
.CodeRay .pseudo-class{color:#555}
.CodeRay .directive{font-weight:bold}
.CodeRay .type{font-weight:bold}
.CodeRay .predefined-type{color:inherit}
.CodeRay .reserved,.CodeRay .keyword{color:#000;font-weight:bold}
.CodeRay .key{color:#808}
.CodeRay .key .delimiter{color:#606}
.CodeRay .key .char{color:#80f}
.CodeRay .value{color:#088}
.CodeRay .regexp .delimiter{color:#808}
.CodeRay .regexp .content{color:#808}
.CodeRay .regexp .modifier{color:#808}
.CodeRay .regexp .char{color:#d14}
.CodeRay .regexp .function{color:#404;font-weight:bold}
.CodeRay .string{color:#d20}
.CodeRay .string .string .string{background:#ffd0d0}
.CodeRay .string .content{color:#d14}
.CodeRay .string .char{color:#d14}
.CodeRay .string .delimiter{color:#d14}
.CodeRay .shell{color:#d14}
.CodeRay .shell .delimiter{color:#d14}
.CodeRay .symbol{color:#990073}
.CodeRay .symbol .content{color:#a60}
.CodeRay .symbol .delimiter{color:#630}
.CodeRay .tag{color:teal}
.CodeRay .tag-special{color:#d70}
.CodeRay .variable{color:#036}
.CodeRay .insert{background:#afa}
.CodeRay .delete{background:#faa}
.CodeRay .change{color:#aaf;background:#007}
.CodeRay .head{color:#f8f;background:#505}
.CodeRay .insert .insert{color:#080}
.CodeRay .delete .delete{color:#800}
.CodeRay .change .change{color:#66f}
.CodeRay .head .head{color:#f4f}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>The bad way to play chess: 3D physics fun using Castle Game Engine (Part 2)</h1>
<div class="details">
<span id="author" class="author">Michalis Kamburelis</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#_introduction">1. Introduction</a></li>
<li><a href="#_coding_the_game">2. Coding the game</a></li>
<li><a href="#_exercises">3. Exercises</a>
<ul class="sectlevel2">
<li><a href="#_handle_a_key_press_to_change_position_of_an_object">3.1. Handle a key press to change position of an object</a></li>
<li><a href="#_push_the_chess_piece_using_physics">3.2. Push the chess piece using physics</a></li>
</ul>
</li>
<li><a href="#_make_code_aware_what_is_a_chess_piece_using_behaviors">4. Make code aware "what is a chess piece" using behaviors</a></li>
<li><a href="#_selecting_3d_object_using_the_mouse">5. Selecting 3D object using the mouse</a>
<ul class="sectlevel2">
<li><a href="#_highlight_the_chess_piece_under_mouse_and_allow_selecting_it">5.1. Highlight the chess piece under mouse and allow selecting it</a></li>
<li><a href="#_sidenote_other_ways_to_show_a_highlight">5.2. Sidenote: Other ways to show a highlight</a></li>
<li><a href="#_sidenote_shadows">5.3. Sidenote: Shadows</a></li>
</ul>
</li>
<li><a href="#_let_user_choose_the_angle_and_strength_to_flick_the_chess_piece">6. Let user choose the angle and strength to flick the chess piece</a>
<ul class="sectlevel2">
<li><a href="#_designing_a_3d_arrow">6.1. Designing a 3D arrow</a></li>
<li><a href="#_add_the_arrow_to_the_main_design">6.2. Add the arrow to the main design</a></li>
<li><a href="#_letting_user_control_the_arrow">6.3. Letting user control the arrow</a></li>
</ul>
</li>
<li><a href="#_flick_that_chess_piece">7. Flick that chess piece!</a></li>
<li><a href="#_conclusion_and_future_ideas">8. Conclusion and future ideas</a></li>
</ul>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="_introduction">1. Introduction</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Welcome to the second part of the article about creating a simple 3D physics game using <em>Castle Game Engine</em>.</p>
</div>
<div class="paragraph">
<p><em>Castle Game Engine</em> is a cross-platform (desktop, mobile, consoles) 3D and 2D game engine using modern Pascal. It&#8217;s free and open-source and works with both FPC and Delphi.</p>
</div>
<div class="paragraph">
<p>In the first part, we learned how to use the visual editor and we have designed a chessboard with chess pieces. Then we used physics to throw the chess piece, such that it collides and knocks down other chess pieces. Remember this is a <em>bad</em> way to play chess. But it&#8217;s really fun!</p>
</div>
<div class="paragraph">
<p>If you have missed the first part, you can still "jump in" at this point. Just download <em>Castle Game Engine</em> from <a href="https://castle-engine.io/" class="bare">https://castle-engine.io/</a> and either set up the chessboard and chess pieces yourself, or use our ready example project from <a href="https://github.com/castle-engine/bad-chess/" class="bare">https://github.com/castle-engine/bad-chess/</a> in the subdirectory <code>project/version_1_designed_in_editor</code>. This project version is a good starting point for this article part.</p>
</div>
<div class="paragraph">
<p>We encourage you to follow this article and perform all the steps yourself, to create a similar toy. If you ever get stuck, you can look at the finished project. It is available in the subdirectory <code>project/version_2_with_code</code> in the same repository, <a href="https://github.com/castle-engine/bad-chess/" class="bare">https://github.com/castle-engine/bad-chess/</a> . It&#8217;s the final project, with everything described in this article done and working.</p>
</div>
<div class="paragraph">
<p>And if you really just want to play the worst version of chess, <em>right now</em>, you can download the ready compiled game (for Linux or Windows) from <a href="https://castle-engine.itch.io/bad-chess" class="bare">https://castle-engine.itch.io/bad-chess</a> . Enjoy!</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_coding_the_game">2. Coding the game</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The focus of this part is to learn how to use Pascal code to make things happen in your game.</p>
</div>
<div class="paragraph">
<p>The core of <em>Castle Game Engine</em> is just a set of Pascal units that can be compiled using FPC and Delphi. Thus the games we create are also just regular Pascal programs that happen to use a few <em>Castle Game Engine</em> units. This means that you can use the workflow you already know and like, with whatever Pascal text editor and compiler you prefer.</p>
</div>
<div class="paragraph">
<p>In particular we support <em>Delphi</em>, <em>Lazarus</em>, <em>VS Code</em> or any other custom editor (like <em>Emacs</em>). We have a dedicated documentation with some IDE-specific hints on <a href="https://castle-engine.io/manual_ide.php" class="bare">https://castle-engine.io/manual_ide.php</a> . Basically just open in <em>Castle Game Engine</em> editor the panel <em>"Preferences &#8594; Code Editor"</em>, configure there which Pascal IDE you use, and everything should work out-of-the-box.  If you double-click on a Pascal file from CGE editor, it will open in the text editor you configured.</p>
</div>
<div class="paragraph">
<p>Specifically for <em>VS Code</em> users, we feature a dedicated extension providing perfect <em>Castle Game Engine</em> integration with <em>VS Code</em>. It provides Pascal syntax highlighting, code completion and ability to compile, run or debug a <em>Castle Game Engine</em> project straight from <em>VS Code</em>. Head on to <a href="https://castle-engine.io/vscode" class="bare">https://castle-engine.io/vscode</a> for links and more documentation.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/editor_and_vscode.png" alt="CGE editor and VS Code">
</div>
</div>
<div class="paragraph">
<p>Note that, while the focus of this chapter is to write Pascal code, we do not stop using <em>Castle Game Engine</em> editor. There are a few things you can do in the editor to make the design "friendly" to the code manipulation and we will explore them in this article. So writing Pascal code, and editing the design visually, go hand-in-hand.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_exercises">3. Exercises</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_handle_a_key_press_to_change_position_of_an_object">3.1. Handle a key press to change position of an object</h3>
<div class="paragraph">
<p>Let&#8217;s start simple. First goal: When user presses a key <code>x</code>, we want to move the <em>black king</em> chess piece a bit higher. It&#8217;s a simple test that we can:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>React to user input (key press).</p>
</li>
<li>
<p>In response, do something interesting in 3D world (move a chess piece).</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Most of the code you write in <em>Castle Game Engine</em> is placed in a unit associated with a <em>view</em>. We talked about what is a <em>view</em> in <em>Castle Game Engine</em> in the previous article part, the short recap is that you use views similar to how you use <em>forms</em> in a typical Delphi FMX / VCL or Lazarus LCL application: a view is a <em>visual design</em> (in <code>data/gameviewmain.castle-user-interface</code>) and associated code (in <code>code/gameviewmain.pas</code>).</p>
</div>
<div class="paragraph">
<p>So let&#8217;s open the file <code>code/gameviewmain.pas</code> in your favorite Pascal IDE. In the <em>Castle Game Engine</em> editor, you can just use the bottom <em>"Files"</em> panel. Enter the <code>code</code> subdirectory and double-click on the <code>gameviewmain.pas</code> file. Alternatively, you can just open your Pascal IDE and from it open the Pascal project. The basic project files (like <code>my_project.dproj</code> for Delphi or <code>my_project.lpi</code> for Lazarus) have been already generated for you.</p>
</div>
<div class="paragraph">
<p>Keep the <em>Castle Game Engine</em> visual editor open too, with our view design <code>data/gameviewmain.castle-user-interface</code> . We will occasionally adjust or consult our visual design, to make sure it is useful for our code logic.</p>
</div>
<div class="paragraph">
<p>For start, we want to know the <em>name</em> of the component representing the black king. Just as you&#8217;ve seen when designing Lazarus and Delphi forms, every component has a <em>name</em> which corresponds to how this component can be accessed from code. You can edit the component name in <em>Castle Game Engine</em> by either editing the <code>Name</code> row in <em>Object Inspector</em> (on the right) or editing the name in the hierarchy (on the left). Simply click on the component name in hierarchy or press F2 to go into name editing. On the screenshot below, you can see that black king is named <code>SceneBlackKing1</code>. I can use Ctrl+C to copy this to the clipboard.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/name.png" alt="Editing name">
</div>
</div>
<div class="paragraph">
<p>Note that, for this first code exercise, we assume that the chess piece (<code>SceneBlackKing1</code>) does <strong>not</strong> have any physics components. If you have added <code>TCastleRigidBody</code> or <code>TCastleXxxCollider</code> components as behaviors of <code>SceneBlackKing1</code>, please remove them for now. We will restore them in the next exercise.</p>
</div>
<div class="paragraph">
<p>Now we have to declare the variable with the exact same name in the view. It will be automatically initialized to point to the component when we start the view. Do this in the <code>published</code> section of the class <code>TViewMain</code>. This is how the end result should look like:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">uses</span> Classes,
  CastleVectors, CastleComponentSerialize,
  CastleUIControls, CastleControls, CastleKeysMouse, CastleScene;

<span class="keyword">type</span>
  <span class="comment">{ Main view, where most of the application logic takes place. }</span>
  TViewMain = <span class="keyword">class</span>(TCastleView)
  <span class="directive">published</span>
    <span class="comment">{ Components designed using CGE editor.
      These fields will be automatically initialized at Start. }</span>
    LabelFps: TCastleLabel;
    SceneBlackKing1: TCastleScene; <span class="comment">//&lt; new line</span>
  <span class="directive">public</span>
  ...</code></pre>
</div>
</div>
<div class="paragraph">
<p>Note: Right now, the <em>Castle Game Engine</em> editor doesn&#8217;t do this automatically for you. That is, we don&#8217;t automatically update your Pascal sources to declare all the components. We have a plan to do this soon. The user experience will have to be a bit different than on Delphi and Lazarus forms, because the game visual designs can easily have hundredths of components that are <em>not supposed to be used from code</em>, so synchronizing them <em>all</em> with Pascal code would create unnecessary noise in your Pascal unit. We will instead make a button to only expose a subset of designed components for code.</p>
</div>
<div class="paragraph">
<p>Once you have declared the <em>published field</em>, we can access the <code>SceneBlackKing1</code> from code, getting and setting its properties, calling its methods anywhere we like. For this exercise, let&#8217;s modify the <code>Translation</code> property of our chess piece, which changes the <em>position</em> of the object.</p>
</div>
<div class="paragraph">
<p>It is a property of type <code>TVector3</code>. <code>TVector3</code> is an advanced record in <em>Castle Game Engine</em> that represents 3D vector&#8201;&#8212;&#8201;in this case a position, but we use it in many other cases too, e.g. to represent a direction or even RGB color. There are a number of useful things defined to help you work with <code>TVector3</code>, in particular:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>Vector3(&#8230;&#8203;)</code> function returns a new <code>TVector3</code> value with given coordinates.</p>
</li>
<li>
<p>The arithmetic operators like <code>+</code> work with <code>TVector3</code> values.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>This means that we can easily move object by writing a code like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">SceneBlackKing1.Translation := SceneBlackKing1.Translation + Vector3(<span class="integer">0</span>, <span class="integer">1</span>, <span class="integer">0</span>);</code></pre>
</div>
</div>
<div class="paragraph">
<p>Where to put this statement? In general, you can use this code anywhere in your view (as long as it executes only after the view has been started). In this case, we want to react to user pressing a key <code>x</code>. To achieve this, we can edit the <code>TViewMain.Press</code> method in the view. The empty implementation of this method is already present, with some helpful comments, so we can just fill it with our code:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">function</span> TViewMain.Press(<span class="keyword">const</span> Event: TInputPressRelease): Boolean;
<span class="keyword">begin</span>
  Result := <span class="keyword">inherited</span>;
  <span class="keyword">if</span> Result <span class="keyword">then</span> Exit; <span class="comment">// allow the ancestor to handle keys</span>

  <span class="keyword">if</span> Event.IsKey(keyX) <span class="keyword">then</span>
  <span class="keyword">begin</span>
    SceneBlackKing1.Translation := SceneBlackKing1.Translation + Vector3(<span class="integer">0</span>, <span class="integer">1</span>, <span class="integer">0</span>);
    Exit(true); <span class="comment">// key was handled</span>
  <span class="keyword">end</span>;
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Build and run the game (e.g. by pressing F9 in <em>Castle Game Engine</em> editor, or in Delphi, or in Lazarus) and press <code>X</code> to see how it works.</p>
</div>
</div>
<div class="sect2">
<h3 id="_push_the_chess_piece_using_physics">3.2. Push the chess piece using physics</h3>
<div class="paragraph">
<p>Let&#8217;s do one more exercise. Let&#8217;s make sure we can use code to push (flick, throw) a chess piece using physics. The chess piece we push, and the direction in which we push it, will be hardcoded in this exercise. But we will get confidence that we can use physics from Pascal code.</p>
</div>
<div class="paragraph">
<p>Let&#8217;s use the black king again.</p>
</div>
<div class="paragraph">
<p>To do this, make sure to add the physics components to the relevant chess piece. We described how to do this in 1st article part, the quick recap is to right-click on the component (<code>SceneBlackKing1</code> in this case) and from the context menu choose <em>"Add Behavior &#8594; Physics &#8594; Collider &#8594; Box (TCastleBoxCollider)"</em>. Make sure you also have physics (with <code>TCastleMeshCollider</code>) active on the chess board, otherwise the chess piece would fall down due to gravity as soon as you run the game.</p>
</div>
<div class="paragraph">
<p>This is how it should look like:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/chess_piece_physics.png" alt="Chess piece with physics components">
</div>
</div>
<div class="paragraph">
<p>To push it using physics, we want to use the <code>ApplyImpulse</code> method of the <code>TCastleRigidBody</code> component associated with the chess piece.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>You can get the <code>TCastleRigidBody</code> component using the <code>SceneBlackKing1.FindBehavior(TCastleRigidBody)</code> method, as shown below.</p>
<div class="paragraph">
<p>Alternatively, you could also declare and access <code>RigidBody1: TCastleRigidBody</code> reference in the published section of your view. We don&#8217;t show this approach here, just because using the <code>FindBehavior</code> seems more educational at this point, i.e. you will find the <code>FindBehavior</code> useful in more situations.</p>
</div>
</li>
<li>
<p>The <code>ApplyImpulse</code> method takes two parameters: the direction of the impulse (as <code>TVector3</code>; length of this vector determines the impulse strength) and the position from which the impulse comes (it is simplest to just use the chess piece position here).</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>In the end, this is the modified version of <code>TViewMain.Press</code> that you should use:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">function</span> TViewMain.Press(<span class="keyword">const</span> Event: TInputPressRelease): Boolean;
<span class="keyword">var</span>
  MyBody: TCastleRigidBody;
<span class="keyword">begin</span>
  Result := <span class="keyword">inherited</span>;
  <span class="keyword">if</span> Result <span class="keyword">then</span> Exit; <span class="comment">// allow the ancestor to handle keys</span>

  <span class="keyword">if</span> Event.IsKey(keyX) <span class="keyword">then</span>
  <span class="keyword">begin</span>
    MyBody := SceneBlackKing1.FindBehavior(TCastleRigidBody) <span class="keyword">as</span> TCastleRigidBody;
    MyBody.ApplyImpulse(Vector3(<span class="integer">0</span>, <span class="integer">10</span>, <span class="integer">0</span>), SceneBlackKing1.WorldTranslation);
    Exit(true); <span class="comment">// key was handled</span>
  <span class="keyword">end</span>;
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Above we use the direction <code>Vector3(0, 10, 0)</code> which means "up, with strength 10". You can experiment with different directions and strengths. If we&#8217;d like to push the chess piece horizontally we would use a direction with non-zero X and/or Z values, and leave Y axis zero.</p>
</div>
<div class="paragraph">
<p>To the <code>uses</code> clause, add also <code>CastleTransform</code> unit, to have <code>TCastleRigidBody</code> class defined.</p>
</div>
<div class="paragraph">
<p>As usual, run the game and test. Pressing <code>X</code> should now bump the chess piece up.</p>
</div>
<div class="paragraph">
<p>You can press <code>X</code> repeatedly, even when the chess piece is already in the air. As you can see in the code&#8201;&#8212;&#8201;we don&#8217;t secure from it, so we allow to push an object that is already flying. We will not cover it in this exercise, but you could use <code>MyBody.PhysicsRayCast</code> to cast a ray with direction <code>Vector3(0, -1, 0)</code> and see whether the chess piece is already in the air.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/chess_piece_thrown.png" alt="Chess piece thrown up">
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_make_code_aware_what_is_a_chess_piece_using_behaviors">4. Make code aware "what is a chess piece" using behaviors</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To implement our desired logic, the code has to somehow know <em>"what is a chess piece"</em>. So far, our 3D world is a collection of <code>TCastleScene</code> components, but it does not give us enough information to distinguish between chess pieces and other objects (like a chessboard). We want to do something crazy, but we don&#8217;t want to flip the chessboard! At least not this time :)</p>
</div>
<div class="paragraph">
<p>To "mark" that the given <code>TCastleScene</code> component is a chess pieces we will invent a new class called <code>TChessPieceBehavior</code> descending from the <code>TCastleBehavior</code> class. We will then attach instances of this class to the <code>TCastleScene</code> components that represent chess pieces. In the future this class can have more fields (holding information specific to this chess piece) and methods. For start, the mere <em>existence</em> of <code>TCastleBehavior</code> instance attached to a scene indicates <em>"this is a chess piece"</em>.</p>
</div>
<div class="paragraph">
<p>To know more about how our <em>behaviors</em> work, see <a href="https://castle-engine.io/behaviors" class="bare">https://castle-engine.io/behaviors</a> for documentation and examples. You can also create a new project from the <em>"3D FPS Game"</em> template and see how the <code>TEnemy</code> class (descendant of <code>TCastleBehavior</code>) is defined and used. The <em>behaviors</em> are a very flexible concept to add information and mechanics to your world and we advise to use them in many situations.</p>
</div>
<div class="paragraph">
<p>There&#8217;s really nothing difficult about our initial <code>TChessPieceBehavior</code> definition. It is almost an empty class. I decided to only add there a <code>Boolean</code> field that says whether the chess piece is white or black:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">type</span>
  TChessPieceBehavior = <span class="keyword">class</span>(TCastleBehavior)
  <span class="directive">public</span>
    Black: Boolean;
  <span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>You can declare it at the beginning of the <code>interface</code> section of unit <code>GameViewMain</code>. Though larger behavior classes may deserve to be placed in their own units.</p>
</div>
<div class="paragraph">
<p>How to attach the behavior instances to the scenes?</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>You could do this visually, by registering the <code>TChessPieceBehavior</code> class in the <em>Castle Game Engine</em> editor.</p>
<div class="paragraph">
<p>This is a very powerful method as it allows to visually add and configure the behavior properties. See the <a href="https://castle-engine.io/custom_components" class="bare">https://castle-engine.io/custom_components</a> for description how to use this.</p>
</div>
</li>
<li>
<p>Or you can do it from code. In this article, I decided to go with this approach.</p>
<div class="paragraph">
<p>This is a bit easier if you have to effectively attach the behavior 32 times, to all the chess pieces, and there&#8217;s no need to specifically configure the initial state of the behavior. Clicking 32 times <em>"Add Behavior"</em> would be a bit tiresome and also unnecessary in our simple case (for this demo, all chess pieces really work the same), so let&#8217;s instead utilize code to easily initialize the chess pieces.</p>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>To attach a behavior to our <code>SceneBlackKing1</code>, we would just create the instance of <code>TChessPieceBehavior</code> in our views&#8217;s <code>Start</code> method, and add using <code>SceneBlackKing1.AddBehavior</code>. Like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">procedure</span> TViewMain.Start;
<span class="keyword">var</span>
  ChessPiece: TChessPieceBehavior;
<span class="keyword">begin</span>
  <span class="keyword">inherited</span>;
  ChessPiece := TChessPieceBehavior.Create(FreeAtStop);
  ChessPiece.Black := true;
  SceneBlackKing1.AddBehavior(ChessPiece);
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>But this is not good enough for our application. Above we added <code>TChessPieceBehavior</code> to only one chess piece. We want to add it to all 32 the chess pieces. How to do it easily? We need to somehow iterate over all the chess pieces. And to set the <code>Black</code> boolean field, we also should somehow know whether this is black or white piece. There are multiple solutions:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>We could assume that all chess pieces have names like <code>SceneWhiteXxx</code> or <code>SceneBlackXxx</code>. Then we can iterate over <code>Viewport1.Items</code> children, and check if their <code>Name</code> starts with given prefix.</p>
</li>
<li>
<p>Or we could look at <code>Tag</code> value of scenes, and have a convention e.g. that <code>Tag = 1</code> means black chess piece, <code>Tag = 2</code> means white chess piece, and other tags (<code>Tag = 0</code> is default, in particular) mean that this is not a chess piece.</p>
</li>
<li>
<p>We could also introduce additional transformation components that group black chess pieces separately from white chess pieces and separately from other stuff (like a chessboard).</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>I decided to go with the latter approach, as introduction of <em>"additional <code>TCastleTransform</code> components to group existing ones"</em> is a powerful mechanism in many other situations. E.g. you can then easily hide or show a given group (using <code>TCastleTransform.Exists</code>) property.</p>
</div>
<div class="paragraph">
<p>To make this happen, right-click on <code>Viewport1.Items</code>, and choose from the context menu <em>"Add Transform &#8594; Transform (TCastleTransform)"</em>.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/adding_transform.png" alt="Adding new transform">
</div>
</div>
<div class="paragraph">
<p>Name this new component <code>BlackPieces</code>. Then drag-and-drop in the editor hierarchy all the black chess pieces (<code>SceneBlackXxx</code> components) to be children of <code>BlackPieces</code>. You can easily select all 16 scenes representing black pieces in the hierarchy by holding the <em>Shift</em> key and then drag-and-drop them all at once into <code>BlackPieces</code>.</p>
</div>
<div class="paragraph">
<p>The end result should look like this in the hierarchy:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/black_pieces_group.png" alt="Black pieces group">
</div>
</div>
<div class="paragraph">
<p>Don&#8217;t worry that only the <code>SceneBlackKing1</code> has the physics components. We will set the physics components using code soon too.</p>
</div>
<div class="paragraph">
<p>Now repeat the process to add a <code>WhitePieces</code> group.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/white_pieces_group.png" alt="White pieces group">
</div>
</div>
<div class="paragraph">
<p>This preparation in the editor makes our code task easier. Add to the published section of <code>TViewMain</code> declaration of <code>BlackPieces</code> and <code>WhitePieces</code> fields, of type <code>TCastleTransform</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">  TViewMain = <span class="keyword">class</span>(TCastleView)
  <span class="directive">published</span>
    ... <span class="comment">// keep other fields too</span>
    BlackPieces, WhitePieces: TCastleTransform;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Now iterate over the 2 chess pieces' groups in the <code>Start</code> method:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">procedure</span> TViewMain.Start;

  <span class="keyword">procedure</span> ConfigureChessPiece(<span class="keyword">const</span> Child: TCastleTransform; <span class="keyword">const</span> Black: Boolean);
  <span class="keyword">var</span>
    ChessPiece: TChessPieceBehavior;
  <span class="keyword">begin</span>
    ChessPiece := TChessPieceBehavior.Create(FreeAtStop);
    ChessPiece.Black := Black;
    Child.AddBehavior(ChessPiece);
  <span class="keyword">end</span>;

<span class="keyword">var</span>
  Child: TCastleTransform;
<span class="keyword">begin</span>
  <span class="keyword">inherited</span>;
  <span class="keyword">for</span> Child <span class="keyword">in</span> BlackPieces <span class="keyword">do</span>
    ConfigureChessPiece(Child, true);
  <span class="keyword">for</span> Child <span class="keyword">in</span> WhitePieces <span class="keyword">do</span>
    ConfigureChessPiece(Child, false);
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>It seems prudent to add basic "sanity check" at this point. Let&#8217;s log the number of chess pieces each side has. Add the following code and the end of the <code>Start</code> method:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">WritelnLog(<span class="string"><span class="delimiter">'</span><span class="content">Configured %d black and %d white chess pieces</span><span class="delimiter">'</span></span>, [
  BlackPieces.Count,
  WhitePieces.Count
]);</code></pre>
</div>
</div>
<div class="paragraph">
<p>To make <code>WritelnLog</code> available, add <code>CastleLog</code> unit to the uses clause. Now when you run the game, you should see a log</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code>Configured 16 black and 16 white chess pieces</code></pre>
</div>
</div>
<div class="paragraph">
<p>On my first run, I actually saw that I have 17 chess pieces on each side by accident. I mistakenly added 3 knights instead of 2 (one knight was at exactly the same position as another, so it wasn&#8217;t obvious). I have removed the excessive knight pieces thanks to this log. Detecting such mistakes is exactly the reason why we add logs and test&#8201;&#8212;&#8201;so I encourage you to do it too.</p>
</div>
<div class="paragraph">
<p>While we&#8217;re at it, we can also use this opportunity to make sure all chess pieces have  physics components (<code>TCastleRigidBody</code> and <code>TCastleBoxCollider</code>). So you don&#8217;t need to manually add them all. This is a reasonable approach if the components don&#8217;t need any manual adjustment per-chess-piece.</p>
</div>
<div class="paragraph">
<p>To do this, extend our <code>ConfigureChessPiece</code> method:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">  <span class="keyword">procedure</span> ConfigureChessPiece(<span class="keyword">const</span> Child: TCastleTransform; <span class="keyword">const</span> Black: Boolean);
  <span class="keyword">begin</span>
    ... <span class="comment">// keep previous code too</span>
    if Child.FindBehavior(TCastleRigidBody) = <span class="keyword">nil</span> <span class="keyword">then</span>
      Child.AddBehavior(TCastleRigidBody.Create(FreeAtStop));
    <span class="keyword">if</span> Child.FindBehavior(TCastleCollider) = <span class="keyword">nil</span> <span class="keyword">then</span>
      Child.AddBehavior(TCastleBoxCollider.Create(FreeAtStop));
  <span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>As you see above, this approach is quite direct: if you don&#8217;t have the necessary component, just add it. We don&#8217;t bother to configure any property on the new <code>TCastleRigidBody</code> and <code>TCastleBoxCollider</code> instances, as their defaults are good for our purpose.</p>
</div>
<div class="paragraph">
<p>This was all a good "ground work" for the remaining article part. Nothing functionally new has actually happened in our game, you should run it and see that&#8230;&#8203; nothing changed. All 32 chess pieces just stand still, at the beginning.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_selecting_3d_object_using_the_mouse">5. Selecting 3D object using the mouse</h2>
<div class="sectionbody">
<div class="sect2">
<h3 id="_highlight_the_chess_piece_under_mouse_and_allow_selecting_it">5.1. Highlight the chess piece under mouse and allow selecting it</h3>
<div class="paragraph">
<p>To implement the real interaction, we want to allow user to choose which chess piece to flick using the mouse. <em>Castle Game Engine</em> provides a ready function that tells you what is being indicated by the current mouse (or last touch, on mobile) position. This is the <code>TCastleViewport.TransformUnderMouse</code> function.</p>
</div>
<div class="paragraph">
<p>For start, make sure to declare the viewport instance in the <code>published</code> section of class <code>TViewMain</code>, like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">MainViewport: TCastleViewport;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Match the name of your viewport in the design. Add unit <code>CastleViewport</code> to the <code>uses</code> clause to make type <code>TCastleViewport</code> known.</p>
</div>
<div class="paragraph">
<p>Let&#8217;s utilize it to highlight the current chess piece at the mouse position. We can just keep checking the <code>MainViewport.TransformUnderMouse</code> value in each <code>Update</code> call.</p>
</div>
<div class="paragraph">
<p>Note: Alternatively, we could check <code>MainViewport.TransformUnderMouse</code> in each <code>Motion</code> call, that occurs only when mouse (or touch) position changes. But doing it in <code>Update</code> is a bit better: as we use physics, some chess pieces may still be moving due to physics, so the chess piece under the mouse may change even if the mouse position doesn&#8217;t change.</p>
</div>
<div class="paragraph">
<p>To actually show the highlight, we will use a ready effect available for every <code>TCastleScene</code> that can be activated by setting <code>MyScene.RenderOptions.WireframeEffect</code> to something else than <code>weNormal</code>. This is the simplest way to show the highlight (we discuss other ways in later section).</p>
</div>
<div class="paragraph">
<p>Before we jump into code, I encourage to experiment with perfect settings of <code>RenderOptions</code> for highlight in the editor. Just edit any chosen chess piece, until it seems to have a pretty highlight, and remember the chosen options. The most useful properties to adjust are <code>WireframeEffect</code>, <code>WireframeColor</code>, <code>LineWidth</code>, <code>SilhouetteBias</code>, <code>SilhouetteScale</code>. You can see them emphasized below&#8201;&#8212;&#8201;editor shows properties which have non-default values using the <strong>bold</strong> font.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/render_options.png" alt="Render options">
</div>
</div>
<div class="paragraph">
<p>I decided to show the currently highlighted (at mouse position) chess piece with a light-blue wireframe. This chess piece is also set as the value of private field <code>ChessPieceHover</code>.</p>
</div>
<div class="paragraph">
<p>Moreover, once user clicks with mouse (we can detect it in <code>Press</code>) the chess piece is considered <em>selected</em> and gets a yellow highlight. This chess piece is set as <code>ChessPieceSelected</code> value.</p>
</div>
<div class="paragraph">
<p>Remembering the <code>ChessPieceHover</code> and <code>ChessPieceSelected</code> values is useful for a few things. For one thing, we can later disable the effect (when the piece is no longer highlighted or selected). And it will allow to flick the <code>ChessPieceSelected</code> in the next sections.</p>
</div>
<div class="paragraph">
<p>We could store them as references to <code>TCastleScene</code> or <code>TChessPieceBehavior</code>. That is, we could declare:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Either <code>ChessPieceHover, ChessPieceSelected: TChessPieceBehavior;</code>&#8230;&#8203;</p>
</li>
<li>
<p>&#8230;&#8203;or <code>ChessPieceHover, ChessPieceSelected: TCastleScene;</code></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>Both declarations would be good for our application. That is, we have to choose one or the other as it will imply a bit different code, but the differences are really minor. In the end, we can always get <code>TChessPieceBehavior</code> instance from a corresponding <code>TCastleScene</code> (if we know it is a chess piece) and we can get <code>TCastleScene</code> from a <code>TChessPieceBehavior</code>.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>To get <code>TChessPieceBehavior</code> from the corresponding <code>TCastleScene</code> you would do:</p>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">var</span>
  MyBehavior: TChessPieceBehavior;
  MyScene: TCastleScene;
<span class="keyword">begin</span>
  ...
  MyBehavior := MyScene.FindBehavior(TChessPieceBehavior) <span class="keyword">as</span> TChessPieceBehavior;</code></pre>
</div>
</div>
</li>
<li>
<p>To get <code>TCastleScene</code> from corresponding <code>TChessPieceBehavior</code> you would do:</p>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">var</span>
  MyBehavior: TChessPieceBehavior;
  MyScene: TCastleScene;
<span class="keyword">begin</span>
  ...
  MyScene := MyBehavior.Parent <span class="keyword">as</span> TCastleScene;</code></pre>
</div>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>I decided to declare them as <code>TChessPieceBehavior</code>. If you want to follow my approach exactly, add this to the <code>private</code> section of class <code>TViewMain</code>:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">ChessPieceHover, ChessPieceSelected: TChessPieceBehavior;
<span class="comment">{ Turn on / off the highlight effect, depending on whether
  Behavior equals ChessPieceHover, ChessPieceSelected or none of them.
  This accepts (and ignores) Behavior = nil value. }</span>
<span class="keyword">procedure</span> ConfigureEffect(<span class="keyword">const</span> Behavior: TChessPieceBehavior);</code></pre>
</div>
</div>
<div class="paragraph">
<p>Then add <code>CastleColors</code> unit to the <code>uses</code> clause (of <code>interface</code> or <code>implementation</code> of unit <code>GameViewMain</code>, doesn&#8217;t matter in this case) to define <code>HexToColorRGB</code> utility.</p>
</div>
<div class="paragraph">
<p>Finally this is the code of new <code>Update</code>, <code>Press</code> and helper <code>ConfigureEffect</code> methods:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">procedure</span> TViewMain.ConfigureEffect(<span class="keyword">const</span> Behavior: TChessPieceBehavior);
<span class="keyword">var</span>
  Scene: TCastleScene;
<span class="keyword">begin</span>
  <span class="keyword">if</span> Behavior = <span class="keyword">nil</span> <span class="keyword">then</span>
    Exit;
  <span class="comment">{ Behavior can be attached to any TCastleTransform.
    But in our case, we know TChessPieceBehavior is attached to TCastleScene. }</span>
  Scene := Behavior.Parent <span class="keyword">as</span> TCastleScene;
  <span class="keyword">if</span> (Behavior = ChessPieceHover) <span class="keyword">or</span>
     (Behavior = ChessPieceSelected) <span class="keyword">then</span>
  <span class="keyword">begin</span>
    Scene.RenderOptions.WireframeEffect := weSilhouette;
    <span class="keyword">if</span> Behavior = ChessPieceSelected <span class="keyword">then</span>
      Scene.RenderOptions.WireframeColor := HexToColorRGB(<span class="string"><span class="delimiter">'</span><span class="content">FFEB00</span><span class="delimiter">'</span></span>)
    <span class="keyword">else</span>
      Scene.RenderOptions.WireframeColor := HexToColorRGB(<span class="string"><span class="delimiter">'</span><span class="content">5455FF</span><span class="delimiter">'</span></span>);
    Scene.RenderOptions.LineWidth := <span class="integer">10</span>;
    Scene.RenderOptions.SilhouetteBias := <span class="integer">20</span>;
    Scene.RenderOptions.SilhouetteScale := <span class="integer">20</span>;
  <span class="keyword">end</span> <span class="keyword">else</span>
  <span class="keyword">begin</span>
    Scene.RenderOptions.WireframeEffect := weNormal;
  <span class="keyword">end</span>;
<span class="keyword">end</span>;

<span class="keyword">procedure</span> TViewMain.Update(<span class="keyword">const</span> SecondsPassed: Single; <span class="keyword">var</span> HandleInput: Boolean);
<span class="keyword">var</span>
  OldHover: TChessPieceBehavior;
<span class="keyword">begin</span>
  <span class="keyword">inherited</span>;

  LabelFps.Caption := <span class="string"><span class="delimiter">'</span><span class="content">FPS: </span><span class="delimiter">'</span></span> + Container.Fps.ToString;

  OldHover := ChessPieceHover;

  <span class="keyword">if</span> MainViewport.TransformUnderMouse &lt;&gt; <span class="keyword">nil</span> <span class="keyword">then</span>
  <span class="keyword">begin</span>
    ChessPieceHover := MainViewport.TransformUnderMouse.FindBehavior(TChessPieceBehavior)
      <span class="keyword">as</span> TChessPieceBehavior;
  <span class="keyword">end</span> <span class="keyword">else</span>
    ChessPieceHover := <span class="keyword">nil</span>;

  <span class="keyword">if</span> OldHover &lt;&gt; ChessPieceHover <span class="keyword">then</span>
  <span class="keyword">begin</span>
    ConfigureEffect(OldHover);
    ConfigureEffect(ChessPieceHover);
  <span class="keyword">end</span>;
<span class="keyword">end</span>;

<span class="keyword">function</span> TViewMain.Press(<span class="keyword">const</span> Event: TInputPressRelease): Boolean;
<span class="keyword">var</span>
  MyBody: TCastleRigidBody;
  OldSelected: TChessPieceBehavior;
<span class="keyword">begin</span>
  Result := <span class="keyword">inherited</span>;
  <span class="keyword">if</span> Result <span class="keyword">then</span> Exit; <span class="comment">// allow the ancestor to handle keys</span>

  <span class="comment">// ... if you want, keep here the handling of keyX from previous exercise</span>

  <span class="keyword">if</span> Event.IsMouseButton(buttonLeft) <span class="keyword">then</span>
  <span class="keyword">begin</span>
    OldSelected := ChessPieceSelected;
    <span class="keyword">if</span> (ChessPieceHover &lt;&gt; <span class="keyword">nil</span>) <span class="keyword">and</span>
       (ChessPieceHover &lt;&gt; ChessPieceSelected) <span class="keyword">then</span>
    <span class="keyword">begin</span>
      ChessPieceSelected := ChessPieceHover;
      ConfigureEffect(OldSelected);
      ConfigureEffect(ChessPieceSelected);
    <span class="keyword">end</span>;
    Exit(true); <span class="comment">// mouse click was handled</span>
  <span class="keyword">end</span>;
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>As always, remember to compile and run the code to make sure it works OK!</p>
</div>
<div class="paragraph">
<p>You will notice that <code>MainViewport.TransformUnderMouse</code> detects what is under the mouse, but treating each chess piece as a box. So the detection is visibly not accurate. To fix this, set <code>PreciseCollisions</code> to <code>true</code> on all the chess pieces. You can do this easily by selecting all chess pieces in editor using <em>Shift</em> or <em>Ctrl</em> and then toggling <code>PreciseCollisions</code> in the <em>Object Inspector</em>.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/precise_collisions.png" alt="CGE editor and VS Code">
</div>
</div>
<div class="paragraph">
<p>I decided to move the camera at this point too (to show both sides, black and white, from a side view).</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/better_camera.png" alt="Camera from the side">
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/highlight.png" alt="Highlight">
</div>
</div>
</div>
<div class="sect2">
<h3 id="_sidenote_other_ways_to_show_a_highlight">5.2. Sidenote: Other ways to show a highlight</h3>
<div class="paragraph">
<p>There are other ways to show the highlighted (or selected) chess piece.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Dynamically changing the material color. Do this by accessing an instance of <code>TPhysicalMaterialNode</code> within the scene&#8217;s nodes (<code>TCastleScene.RootNode</code>) and changing the <code>TPhysicalMaterialNode.BaseColor</code>. See e.g. engine example <code>examples/viewport_and_scenes/collisions/</code> that uses this.</p>
</li>
<li>
<p>Dynamically enabling / disabling a shader effect. This means adding <code>TEffectNode</code> and <code>TEffectPartNode</code> nodes to the scene and implementing the effect using GLSL (<em>OpenGL Shading Language</em>). See <code><a href="https://castle-engine.io/shaders" class="bare">https://castle-engine.io/shaders</a></code> about this.</p>
</li>
<li>
<p>Adding a additional box that surrounds chosen object. The CGE editor itself uses this technique to show highlighted / selected 3D objects. Use <code>TDebugTransformBox</code> class to implement this easily.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>If you are curious, hopefully the above information and examples will point you in the right direction.</p>
</div>
</div>
<div class="sect2">
<h3 id="_sidenote_shadows">5.3. Sidenote: Shadows</h3>
<div class="paragraph">
<p>I decided to activate shadows at this point. Just set <code>Shadows</code> to <code>true</code> on the main light source. Moreover, set <code>RenderOptions.WholeSceneManifold</code> to <code>true</code> at the chess pieces. This should make everything cast nice shadows. The shadows are <em>dynamic</em> which means that they will properly change when we will move the chess pieces.</p>
</div>
<div class="paragraph">
<p>See <a href="https://castle-engine.io/shadow_volumes" class="bare">https://castle-engine.io/shadow_volumes</a> for more information about shadows in <em>Castle Game Engine</em>.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/shadows.png" alt="Shadows">
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_let_user_choose_the_angle_and_strength_to_flick_the_chess_piece">6. Let user choose the angle and strength to flick the chess piece</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Once the user has picked a chess piece, we want to allow configuring the direction and strength with which to <em>flick</em> the chosen object. We already know that <em>"flicking"</em> the chess piece technically means <em>"applying a physics force to the rigid body of a chosen chess piece"</em>. We have almost everything we need, but we need to allow user to choose the direction and strength of this force.</p>
</div>
<div class="sect2">
<h3 id="_designing_a_3d_arrow">6.1. Designing a 3D arrow</h3>
<div class="paragraph">
<p>To visualize the desired <em>force</em> we will use a simple 3D arrow model, that will be rotated and scaled accordingly. While we could design such model in Blender or other 3D authoring software, in this case it&#8217;s easiest to just do it completely in the <em>Castle Game Engine</em> editor. The arrow is a composition of two simple shapes: <em>cone</em> (for the arrow tip) and a <em>cylinder</em>.</p>
</div>
<div class="paragraph">
<p>Moreover let&#8217;s design the arrow independently, as a separate <em>design</em>. The new <em>design</em> will contain a hierarchy of components, with the root being <code>TCastleTransform</code>. We will save it as a file <code>force_gizmo.castle-transform</code> in the project <code>data</code> subdirectory. Then we will add it to the main design (<code>gameviewmain.castle-user-interface</code>), and toggle the existence, rotation and scale of the visualized force.</p>
</div>
<div class="paragraph">
<p>Using a separate design file for the 3D arrow, while not strictly necessary in this case, is a powerful technique. When something is saved as a separate design file, you can reuse it freely, and instantiate it many times (at design-time, or by dynamically <em>spawning</em> during the game run-time). This is e.g. how to have creatures in your game: 3D objects that share common logic and that can be spawned whenever needed.</p>
</div>
<div class="paragraph">
<p>To start designing the arrow, choose editor menu item <em>"Design &#8594; New Transform (Empty Transform as Root)"</em>.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/new_transform.png" alt="Shadows">
</div>
</div>
<div class="paragraph">
<p>Underneath, add two components: <code>TCastleCylinder</code> and <code>TCastleCone</code>.</p>
</div>
<div class="paragraph">
<p>Adjust their <code>Height</code>, <code>Radius</code> (on cylinder), <code>BottomRadius</code> (on cone) and <code>Translation</code> to form a nice 3D arrow.</p>
</div>
<div class="paragraph">
<p>Adjust their <code>Color</code> to something non-default to make things prettier. Remember that the arrow will later be lit by the lights we have set up in the main design (<code>gameviewmain.castle-user-interface</code>), so it will probably be brighter than what you observe now.</p>
</div>
<div class="paragraph">
<p>You can follow the values I chosen on the screenshots below, but really these are just examples. Go ahead and create your own 3D arrow as you please.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/cone.png" alt="Cone">
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/cylinder.png" alt="Cylinder">
</div>
</div>
<div class="paragraph">
<p>Now comes a bit difficult part. We want to have an arrow that can easily <em>rotate around a dummy box</em> (in the actual game, it will rotate around a chess piece). Ideally, an arrow should also easily scale to visualize the force strength. I use the words <em>easily</em> to emphasize that we don&#8217;t want to only rotate it in the editor, but we will also have to allow user to rotate it during the game. So the rotation and scale that are interesting to us must be very easy to get and set from code.</p>
</div>
<div class="paragraph">
<p>To do this, first add a dummy box representing a chess piece. I called it <code>DebugBoxToBeHidden</code> and set <code>Size</code> of the box to <code>2 3 2</code> to account for tall (large Y axis) chess pieces. Later we will make the box hidden by setting its <code>Exists</code> property to <code>false</code>.</p>
</div>
<div class="paragraph">
<p>Once you have a box, you want to add intermediate <code>TCastleTransform</code> components to</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>rotate the arrow (cone and cylinder) to be horizontal</p>
</li>
<li>
<p>move the arrow away from the box</p>
</li>
<li>
<p>rotate the arrow around the box</p>
</li>
<li>
<p>scale the arrow.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>There are multiple valid ways of achieving this. The key advise is to not hesitate to make a nested composition, that is place <code>TCastleTransform</code> within another <code>TCastleTransform</code> within another <code>TCastleTransform</code> and so on. Let each <code>TCastleTransform</code> perform a single function. Take it step by step and you will get to a valid solution (and there are really a number of possible ways to arrange this).</p>
</div>
<div class="paragraph">
<p>See my arrangement on the screenshots below. If you get stuck, just use the design from our resulting project in <a href="https://github.com/castle-engine/bad-chess/" class="bare">https://github.com/castle-engine/bad-chess/</a> (in <code>project/version_2_with_code</code> subdirectory).</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/arrow_1.png" alt="Arrow RotationToMakeForceHorizontal component">
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/arrow_2.png" alt="Arrow TransformForceAngle component">
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/arrow_3.png" alt="Arrow TransformForceStrength component">
</div>
</div>
<div class="paragraph">
<p>The outcome of my design is that I know that from code, I can:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Adjust <code>Rotation</code> property of the <code>TransformForceAngle</code> component to be a simple rotation around the X axis. The angle of this rotation can be chosen by user and effectively the arrow will <em>orbit</em> around the debug box (chess piece).</p>
</li>
<li>
<p>Adjust Y of the <code>Scale</code> property of the <code>TransformForceStrength</code> component. The amount of this scale can be chosen by user to visualize the strength.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Remember to set <code>Exists</code> of the <code>DebugBoxToBeHidden</code> component to <code>false</code> once done.</p>
</div>
</div>
<div class="sect2">
<h3 id="_add_the_arrow_to_the_main_design">6.2. Add the arrow to the main design</h3>
<div class="paragraph">
<p>To test that it works, add the arrow design to the main design using the editor.</p>
</div>
<div class="paragraph">
<p>Save the design <code>force_gizmo.castle-transform</code>, open our main design in <code>gameviewmain.castle-user-interface</code>, select the <code>Items</code> component inside <code>MainViewport</code> and drag-and-drop the file <code>force_gizmo.castle-transform</code> (from the <em>"Files"</em> panel below) on the hierarchy.</p>
</div>
<div class="paragraph">
<p>The result should be that a new component called <code>DesignForceGizmo1</code> is created and placed as a child of <code>Items</code>. The component class is <code>TCastleTransformDesign</code>, which means that it&#8217;s an instance of <code>TCastleTransform</code> loaded from another file with <code>.castle-transform</code> extension. The <code>URL</code> property of this component should automatically be set to indicate our <code>force_gizmo.castle-transform</code> file.</p>
</div>
<div class="paragraph">
<p>Rename this component to just <code>DesignForceGizmo</code> (up to you, but I think it makes things clearer&#8201;&#8212;&#8201;we will only ever need one such gizmo). Moreover, change the <code>Exists</code> property of this component to <code>false</code> because initially, we don&#8217;t want this component to be visible or pickable by the mouse.</p>
</div>
<div class="paragraph">
<p>The screenshot below shows the state <em>right before I set <code>Exists</code> to <code>false</code></em>.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/design_in_main.png" alt="Arrow design added to the main design">
</div>
</div>
</div>
<div class="sect2">
<h3 id="_letting_user_control_the_arrow">6.3. Letting user control the arrow</h3>
<div class="paragraph">
<p>We need to declare and initialize the fields that describe current angle and strength.</p>
</div>
<div class="paragraph">
<p>Add this to the <code>private</code> section of the <code>TViewMain</code> class:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">TransformForceAngle, TransformForceStrength: TCastleTransform;
ForceAngle: Single;
ForceStrength: Single;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Then let&#8217;s set some constants. You can declare them at the beginning of unit <code>GameViewMain</code> implementation:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">const</span>
  MinStrength = <span class="integer">1</span>;
  MaxStrength = <span class="integer">1000</span>;

  MinStrengthScale = <span class="integer">1</span>;
  MaxStrengthScale = <span class="integer">3</span>;

  StrengthChangeSpeed = <span class="integer">30</span>;
  AngleAChangeSpeed = <span class="integer">10</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Add to the <code>uses</code> clause new necessary units: <code>Math</code>, <code>CastleUtils</code>.</p>
</div>
<div class="paragraph">
<p>Finally add to the <code>TViewMain.Start</code> additional piece of code to initialize everything:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi">  TransformForceAngle := DesignForceGizmo.DesignedComponent(<span class="string"><span class="delimiter">'</span><span class="content">TransformForceAngle</span><span class="delimiter">'</span></span>)
    <span class="keyword">as</span> TCastleTransform;
  TransformForceStrength := DesignForceGizmo.DesignedComponent(<span class="string"><span class="delimiter">'</span><span class="content">TransformForceStrength</span><span class="delimiter">'</span></span>)
    <span class="keyword">as</span> TCastleTransform;
  ForceAngle := <span class="integer">0</span>; <span class="comment">// 0 is default value of Single field anyway</span>
  TransformForceAngle.Rotation := Vector4(<span class="integer">1</span>, <span class="integer">0</span>, <span class="integer">0</span>, ForceAngle);
  ForceStrength := <span class="integer">10</span>; <span class="comment">// set some sensible initial value</span>
  TransformForceStrength.Scale := Vector3(<span class="integer">1</span>,
    MapRange(ForceStrength, MinStrength, MaxStrength, MinStrengthScale, MaxStrengthScale),
    <span class="integer">1</span>);</code></pre>
</div>
</div>
<div class="paragraph">
<p>Note that we initialize the components within our <code>DesignForceGizmo</code> design using the <code>DesignForceGizmo.DesignedComponent(&#8230;&#8203;)</code> call. This is necessary, as in general you can have multiple instances of the design <code>force_gizmo.castle-transform</code> placed in your view. So the <code>published</code> fields of the view cannot be automatically associated with components in nested designs.</p>
</div>
<div class="paragraph">
<p>Moreover we synchronize <code>Single</code> fields <code>ForceStrength</code> and <code>ForceAngle</code> with their counterpart <code>TCastleTransform</code> instances. <code>Single</code> in Pascal is a simple floating-point number, which is super-easy to manipulate. We treat two <code>TCastleTransform</code> instances above as just a fancy way to visualize these numbers as 3D rotation and scale.</p>
</div>
<div class="paragraph">
<p>You may want to lookup what the <code>MapRange</code> function does in <em>Castle Game Engine</em> API reference. In short, it&#8217;s a comfortable way of doing a linear interpolation, converting from one range to another.</p>
</div>
<div class="paragraph">
<p>Now that we have initialized everything, let&#8217;s actually show the <code>DesignForceGizmo</code> when user selects a chess piece. We already have a code to select chess piece on mouse click. Just extend it to show the <code>DesignForceGizmo</code> and reposition it at the selected chess piece.</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">if</span> Event.IsMouseButton(buttonLeft) <span class="keyword">then</span>
<span class="keyword">begin</span>
  OldSelected := ChessPieceSelected;
  <span class="keyword">if</span> (ChessPieceHover &lt;&gt; <span class="keyword">nil</span>) <span class="keyword">and</span>
     (ChessPieceHover &lt;&gt; ChessPieceSelected) <span class="keyword">then</span>
  <span class="keyword">begin</span>
    ... <span class="comment">// keep existing code</span>

    <span class="comment">// new lines:</span>
    DesignForceGizmo.Exists := true;
    DesignForceGizmo.Translation := ChessPieceSelected.Parent.WorldTranslation;
  <span class="keyword">end</span>;
  Exit(true); <span class="comment">// mouse click was handled</span>
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Note: You may wonder about an alternative approach, where we don&#8217;t reposition <code>DesignForceGizmo</code>, but instead dynamically change it&#8217;s parent, like <code>DesignForceGizmo.Parent := ChessPieceSelected.Parent</code>. This would work too, alas with some additional complications: the rotation of the selected object, once we flick it, would rotate also the gizmo. This would make the calculation of "desired flick direction" later more complicated. So I decided to go with the simpler approach of just repositioning the <code>DesignForceGizmo</code>. If you want to experiment with the alternative complicated approach, go ahead, one solution would be to design <code>DesignForceGizmo</code> such that you can later do <code>TransformForceAngle.GetWorldView(WorldPos, WorldDir, WorldUp)</code> and use resulting <code>WorldDir</code> as a force direction.</p>
</div>
<div class="paragraph">
<p>But since we keep things simple&#8230;&#8203; we&#8217;re almost done. You can run the game and see that selecting a chess piece shows the arrow gizmo properly. It remains to allow user to change direction and strength. We can do this by observing the keys user presses in the <code>Update</code> method. The code below allows to rotate the arrow (make it orbit around the chess piece) using <em>left</em> and <em>right</em> arrow keys, and change force strength (scaling the arrow) using <em>up</em> and <em>down</em> arrow keys. Add this code to your existing <code>Update</code> method:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">procedure</span> TViewMain.Update(<span class="keyword">const</span> SecondsPassed: Single; <span class="keyword">var</span> HandleInput: Boolean);
<span class="keyword">begin</span>
  ... <span class="comment">// keep existing code</span>
  if Container.Pressed[keyArrowLeft] <span class="keyword">then</span>
    ForceAngle := ForceAngle - SecondsPassed * AngleAChangeSpeed;
  <span class="keyword">if</span> Container.Pressed[keyArrowRight] <span class="keyword">then</span>
    ForceAngle := ForceAngle + SecondsPassed * AngleAChangeSpeed;
  <span class="keyword">if</span> Container.Pressed[keyArrowUp] <span class="keyword">then</span>
    ForceStrength := Min(MaxStrength, ForceStrength + SecondsPassed * StrengthChangeSpeed);
  <span class="keyword">if</span> Container.Pressed[keyArrowDown] <span class="keyword">then</span>
    ForceStrength := Max(MinStrength, ForceStrength - SecondsPassed * StrengthChangeSpeed);

  TransformForceAngle.Rotation := Vector4(<span class="integer">1</span>, <span class="integer">0</span>, <span class="integer">0</span>, ForceAngle);
  TransformForceStrength.Scale := Vector3(<span class="integer">1</span>,
    MapRange(ForceStrength, MinStrength, MaxStrength, MinStrengthScale, MaxStrengthScale),
    <span class="integer">1</span>);
<span class="keyword">end</span>;</code></pre>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_flick_that_chess_piece">7. Flick that chess piece!</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Looks like we have all the knowledge we need.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>We know how to flick the chess piece,</p>
</li>
<li>
<p>we know which chess piece to flick,</p>
</li>
<li>
<p>we know the direction and strength of the flick.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can consult the code we did a few sections before, in the exercise <em>"Push the chess piece using physics</em>". Our new code will be similar. Add it to the <code>Press</code> method implementation:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="CodeRay highlight"><code data-lang="delphi"><span class="keyword">function</span> TViewMain.Press(<span class="keyword">const</span> Event: TInputPressRelease): Boolean;
<span class="keyword">var</span>
  ... <span class="comment">// keep existing variables used by other inputs</span>
  ChessPieceSelectedScene: TCastleScene;
  ForceDirection: TVector3;
<span class="keyword">begin</span>
  Result := <span class="keyword">inherited</span>;
  <span class="keyword">if</span> Result <span class="keyword">then</span> Exit; <span class="comment">// allow the ancestor to handle keys</span>

  ... <span class="comment">// keep existing code handling other inputs</span>

  if Event.IsKey(keyEnter) <span class="keyword">and</span> (ChessPieceSelected &lt;&gt; <span class="keyword">nil</span>) <span class="keyword">then</span>
  <span class="keyword">begin</span>
    ChessPieceSelectedScene := ChessPieceSelected.Parent <span class="keyword">as</span> TCastleScene;
    MyBody := ChessPieceSelectedScene.FindBehavior(TCastleRigidBody) <span class="keyword">as</span> TCastleRigidBody;
    ForceDirection := RotatePointAroundAxis(
      Vector4(<span class="integer">0</span>, <span class="integer">1</span>, <span class="integer">0</span>, ForceAngle), Vector3(-<span class="integer">1</span>, <span class="integer">0</span>, <span class="integer">0</span>));
    MyBody.ApplyImpulse(
      ForceDirection * ForceStrength,
      ChessPieceSelectedScene.WorldTranslation);
    <span class="comment">// unselect after flicking; not strictly necessary, but looks better</span>
    ChessPieceSelected := <span class="keyword">nil</span>;
    DesignForceGizmo.Exists := false;
    Exit(true); <span class="comment">// input was handled</span>
  <span class="keyword">end</span>;
<span class="keyword">end</span>;</code></pre>
</div>
</div>
<div class="paragraph">
<p>Depending on how you designed the <code>force_gizmo.castle-transform</code> design, you may need to adjust the <code>ForceDirection</code> calculation, in particular the 2nd parameter to <code>RotatePointAroundAxis</code> which is a direction used when angle is zero. There&#8217;s nothing magic about our value <code>Vector3(-1, 0, 0)</code>, it just follows our <code>force_gizmo.castle-transform</code> design.</p>
</div>
<div class="paragraph">
<p>Run the game and see that you can now flick the chess pieces!</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Select the chess piece by clicking with mouse.</p>
</li>
<li>
<p>Rotate the force by <em>left</em> and <em>right</em> arrow keys.</p>
</li>
<li>
<p>Change the force strength by <em>up</em> and <em>down</em> arrow keys.</p>
</li>
<li>
<p>Flick the chess piece by pressing <em>Enter</em>.</p>
</li>
<li>
<p>Repeat :)</p>
</li>
</ul>
</div>
<div class="imageblock">
<div class="content">
<img src="images_2/game_4.png" alt="Gameplay">
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_conclusion_and_future_ideas">8. Conclusion and future ideas</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Invite a friend to play with you. Just take turns using the mouse to flick your chess pieces and have fun :)</p>
</div>
<div class="paragraph">
<p>I am sure you can invent now multiple ways to make this better.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Maybe each player should be able to flick only its own chess pieces? We already know which chess piece is black or white (the <code>Black</code> boolean field in <code>TChessPieceBehavior</code>), though we didn&#8217;t use it for anything above. You should track which player flicked the object last (black or white), and only allow to choose the opposite side next time.</p>
</li>
<li>
<p>Maybe you want to display some user interface, like a label, to indicate whose turn is it? Just drop a <code>TCastleLabel</code> component on view, and change the label&#8217;s <code>Caption</code> whenever you want.</p>
</li>
<li>
<p>Maybe you want to show the current force angle and strength&#8201;&#8212;&#8201;either as numbers, or as some colorful bars? Use <code>TCastleRectangleColor</code> for a trivial rectangle with optional border and optionally filled with a color.</p>
</li>
<li>
<p>Maybe you want to implement a proper chess game? Sure, just track in code all the chess pieces and the chessboard tiles&#8201;&#8212;&#8201;what is where. Then add a logic that allows player to select which piece and where should move. Add some validation. Add playing with a computer opponent if you wish&#8201;&#8212;&#8201;there are standardized protocols to communicate with <em>"chess engines"</em> so you don&#8217;t need to implement your own chess AI from scratch.</p>
</li>
<li>
<p>Maybe you want to use networking? You can use a number of networking solutions (any Pascal library) together with <em>Castle Game Engine</em>. See <a href="https://castle-engine.io/multi_player" class="bare">https://castle-engine.io/multi_player</a> . We have used the engine with <em>Indy</em> and <em>RNL (Realtime Network Library)</em>. In the future we plan to integrate the engine with <em>Nakama</em>, an open-source server and client framework for multi-player games.</p>
</li>
<li>
<p>Maybe you want to deploy this game to other platforms, in particular mobile? Go ahead. The code we wrote above is already cross-platform and can be compiled using <em>Castle Game Engine</em> to any Android or iOS. Our build tool does everything for you, you get a ready APK, AAB or IPA file to install on your phone. See the engine documentation on <a href="https://castle-engine.io/android" class="bare">https://castle-engine.io/android</a> .</p>
<div class="paragraph">
<p>Although keyboard inputs will not work on mobile. You need to invent and implement a new user interface to rotate the force, change the strength, and actually throw the chess piece. It is simplest to just show clickable buttons to perform the relevant actions. The <code>TCastleButton</code> class of the engine is a button with a freely customizable look.</p>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>If you want to learn more about the engine, read the documentation on <a href="https://castle-engine.io/" class="bare">https://castle-engine.io/</a> and join our community on forum and Discord: <a href="https://castle-engine.io/talk.php" class="bare">https://castle-engine.io/talk.php</a> . Last but not least, if you like this article and the engine, we will appreciate if you support us on Patreon <a href="https://www.patreon.com/castleengine" class="bare">https://www.patreon.com/castleengine</a> . We really count on your support.</p>
</div>
<div class="paragraph">
<p>Finally, above all, have fun! Creating games is a wild process and experimenting with <em>"what feels good"</em> is the right way to do it. I hope you will enjoy it.</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2025-05-28 01:12:12 +0200
</div>
</div>
</body>
</html>