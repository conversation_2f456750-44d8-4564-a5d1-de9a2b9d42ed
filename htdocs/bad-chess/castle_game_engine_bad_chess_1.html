<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.23">
<meta name="author" content="<PERSON><PERSON><PERSON>">
<title>The bad way to play chess: 3D physics fun using Castle Game Engine (Part 1)</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ul.square{list-style-type:square}
ul.circle ul:not([class]),ul.disc ul:not([class]),ul.square ul:not([class]){list-style:inherit}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child{border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:first-child,.sidebarblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child,.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active,#footnotes .footnote a:first-of-type:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,td.hdlist1,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="article toc2 toc-left">
<div id="header">
<h1>The bad way to play chess: 3D physics fun using Castle Game Engine (Part 1)</h1>
<div class="details">
<span id="author" class="author">Michalis Kamburelis</span><br>
</div>
<div id="toc" class="toc2">
<div id="toctitle">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#_introduction">1. Introduction</a></li>
<li><a href="#_the_real_introduction">2. The Real Introduction</a></li>
<li><a href="#_download_and_install_the_engine">3. Download and install the engine</a></li>
<li><a href="#_create_your_first_project">4. Create your first project</a></li>
<li><a href="#_optionally_tweak_the_editor_preferences">5. Optionally tweak the editor preferences</a></li>
<li><a href="#_learning_to_design_3d_items_in_a_viewport">6. Learning to design 3D items in a viewport</a></li>
<li><a href="#_design_a_3d_chessboard_with_chess_pieces">7. Design a 3D chessboard with chess pieces</a></li>
<li><a href="#_using_physics_in_the_editor">8. Using physics in the editor</a></li>
<li><a href="#_summary">9. Summary</a></li>
</ul>
</div>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="imageblock">
<div class="content">
<img src="images_1/view_chess.png" alt="Chessboard with chess pieces designed in the editor">
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_introduction">1. Introduction</h2>
<div class="sectionbody">
<div class="paragraph">
<p>I remember my first book about chess, when I was a kid. It was a book teaching young people how to play chess. The first chapter started with a tale about children <em>playing chess incorrectly</em>: they didn&#8217;t know the rules, so they put chess pieces randomly on the chessboard, and flicked them with their fingers towards the other side. The wooden chess pieces flew in the air, bashed with each other. Eventually most of the chess pieces fell off the chessboard onto the floor. The person with the last chess piece remaining on the chessboard was the winner.</p>
</div>
<div class="paragraph">
<p>That was naturally a bad way to play chess. In the second chapter of the book, an adult came, told children that they play chess wrong, and taught them the <em>right way</em>&#8201;&#8212;&#8201;how each figure moves, how the king is special, what it means to check and then mate your opponent. The book overall was great, and it&#8217;s likely responsible for my love for chess (the <em>proper</em> version of the game, with rules instead of flicking objects) to this day.</p>
</div>
<div class="paragraph">
<p>That being said&#8230;&#8203; Don&#8217;t you want to play some day this <em>"incorrect"</em> version of chess, the children&#8217;s version, where nothing else matters except just sending each chess piece flying toward the other side?</p>
</div>
<div class="paragraph">
<p>In this series of articles we will go back in time, erase our hard-earned knowledge about how to <em>really</em> play chess, and implement a simple 3D physics fun application where you can flick chess pieces using physics. You can treat it as a game for 2 people&#8201;&#8212;&#8201;just play it on a computer, and let each player use the mouse and keyboard in turn.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_the_real_introduction">2. The Real Introduction</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The real purpose of this article is to be an entertaining but also useful introduction to using <em>Castle Game Engine</em>.</p>
</div>
<div class="paragraph">
<p><em>Castle Game Engine</em> is a cross-platform (desktop, mobile, consoles) 3D and 2D game engine. We will learn how to make a game for desktops (Linux, Windows, macOS, FreeBSD).</p>
</div>
<div class="paragraph">
<p>In the first part of the article we will show how to design a 3D chessboard and chess pieces using <em>Castle Game Engine</em> editor and how to use <em>physics</em>. In the next part, we will do some coding in Pascal to implement the game logic. In future articles we&#8217;d like to show also development for other platforms (like Android and iOS) and future plans (like the <em>web platform</em>).</p>
</div>
<div class="paragraph">
<p>You can use FPC or Delphi to develop the application presented here. In our engine, we are committed to perfect support for both of these Pascal compilers. Though note that with Delphi, you can right now target only Windows and Linux (with FPC, all the platforms are available).</p>
</div>
<div class="paragraph">
<p><em>Castle Game Engine</em> features a powerful visual editor to design your games, in 2D or 3D. Just like Delphi and Lazarus visual libraries, it&#8217;s all based on a simple RAD concept: you can design a functional application easily visually but at the same time <strong>everything you do is actually using Pascal classes and properties</strong>. So all your knowledge gained from using the editor is also useful when you need to write some Pascal code. You will use the same classes and properties in Pascal that you&#8217;ve seen in the visual editor.</p>
</div>
<div class="paragraph">
<p>The engine is free and open-source. Use it to develop open-source or proprietary applications. You can distribute them to friends in any way, you can publish them on Steam, Itch.io, Google Play (Android), AppStore (iOS), your own website&#8201;&#8212;&#8201;everywhere.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_download_and_install_the_engine">3. Download and install the engine</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Start by downloading the engine from our website: <code><a href="https://castle-engine.io/download" class="bare">https://castle-engine.io/download</a></code> . Choose the version suitable for your operating system.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>On Windows, the recommended download is a simple installer. Just run it.</p>
</li>
<li>
<p>On Linux, just unpack the downloaded zip file to any directory you like.</p>
</li>
<li>
<p>Follow our website for more detailed instructions and other platforms.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Once installed, run the <em>Castle Game Engine</em> editor.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>If you used the installer on Windows, then the shortcut to run <em>Castle Game Engine</em> has already been created for you.</p>
</li>
<li>
<p>If you unpacked the engine from a zip file, then run the binary <code>castle-editor</code> from the subdirectory <code>bin</code> where you have unpacked the engine.</p>
</li>
</ul>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/initial_editor.png" alt="Castle Game Engine editor">
</div>
</div>
<div class="paragraph">
<p>If you encounter any issue, consult our manual on <code><a href="https://castle-engine.io/install" class="bare">https://castle-engine.io/install</a></code> .</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_create_your_first_project">4. Create your first project</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Let&#8217;s create a new project. Click on the <em>"New Project"</em> button, choose the <em>"Empty"</em> project template, configure the project name and directory as you wish, and click <em>"Create Project"</em>.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/new_project.png" alt="Creating new project">
</div>
</div>
<div class="paragraph">
<p>In response, we will create a new directory with a few project files that define your project data and initial Pascal code.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/project_view.png" alt="Project">
</div>
</div>
<div class="paragraph">
<p>You can explore the files in your project using the bottom panel of the editor. You can also just explore them using your regular file manager&#8201;&#8212;&#8201;there&#8217;s nothing special about this directory, these are normal files and directories.</p>
</div>
<div class="paragraph">
<p>The most important files and directories are:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>code</code> is a subdirectory where we advise to put all Pascal source code (units) of your application. Initially it contains just 2 units, <code>GameInitialize</code> and <code>GameViewMain</code>.</p>
</li>
<li>
<p><code>data</code> is a subdirectory where you should put all the data that has to be loaded at run-time by your application. All the 3D and 2D models, textures, designs have to be placed here if you want to use them in your game. Initially it contains the <em>design</em> called <code>gameviewmain.castle-user-interface</code> (and, less important, <code>CastleSettings.xml</code> and <code>README.txt</code> files).</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The general idea is that the initial application (created from the <em>"Empty"</em> template) contains just a single <em>view</em> called <code>Main</code>. A <em>view</em> is a <em>Castle Game Engine</em> concept that represents something that can be displayed in a <em>Castle Game Engine</em>  application. You use it typically quite like a <em>form</em> in Delphi or Lazarus. It is a basic way to organize your application.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Every <em>view</em> can be visually designed. Just double-click on it, in the <em>"Open Existing View"</em> panel or in the <em>"Files"</em> panel (when you&#8217;re exploring the <code>data</code> subdirectory).</p>
<div class="paragraph">
<p>This allows to visually design the contents of the <code>gameviewmain.castle-user-interface</code> file. The file has an extension <code>.castle-user-interface</code> because a <em>view</em> is a special case of <em>user interface</em> in Castle Game Engine.</p>
</div>
<div class="paragraph">
<p>In larger applications, you can have multiple views. Also, in larger applications, you can visually design some <em>user interface</em> elements that are not views, but are just reusable pieces of a user interface. All these files have the extension <code>.castle-user-interface</code> and can be visually designed using the editor. The views have, by convention, a name like <code>gameview*.castle-user-interface</code>.</p>
</div>
</li>
<li>
<p>Every view has also an accompanying Pascal unit. The unit is named like the view, but without the <code>.castle-user-interface</code> extension. So in our case, the unit is called <code>gameviewmain.pas</code>. The unit contains the Pascal code that should be executed when the view is displayed. It defines a class that has virtual methods to react to various useful events (like view being started, or user pressing a key or a mouse button). You will often add more methods to it, to implement your application logic.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>See <code><a href="https://castle-engine.io/view_events" class="bare">https://castle-engine.io/view_events</a></code> and <code><a href="https://castle-engine.io/views" class="bare">https://castle-engine.io/views</a></code> to learn more about the <em>views</em> in our engine.</p>
</div>
<div class="paragraph">
<p>To be clear about the terminology used throughout our engine:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A <em>design</em> is a name for <em>a file you can visually design using our editor</em>. A design can be a file with extension:</p>
<div class="ulist">
<ul>
<li>
<p><code>.castle-user-interface</code> (user interface, can be loaded to a class descending from <code>TCastleUserInterface</code>)</p>
</li>
<li>
<p><code>.castle-transform</code> (3D or 2D transformation, can be loaded to a class descending from <code>TCastleTransform</code>)</p>
</li>
<li>
<p><code>.castle-component</code> (any other component; can be loaded to a class descending from <code>TComponent</code>)</p>
</li>
</ul>
</div>
</li>
<li>
<p>A <em>user interface design</em> is a specific case of a <em>design</em> file. It is a file with <code>.castle-user-interface</code> extension.</p>
</li>
<li>
<p>A <em>view</em> is a specific case of a <em>user interface design</em>. By convention it is called like <code>gameview*.castle-user-interface</code>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You&#8217;re probably itching to start actually <em>doing</em> something after this lengthy introduction. Let&#8217;s get to it.</p>
</div>
<div class="paragraph">
<p>As a first thing, make sure that everything works. Use the big <em>"Compile And Run"</em> button (key shortcut <em>F9</em>) and watch as the project is compiled and run. The result will be boring&#8201;&#8212;&#8201;dark window with FPS (<em>frames per second</em>) counter in the top-right corner. FPS are a standard way to measure your application performance.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/first_run.png" alt="First run of the project">
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_optionally_tweak_the_editor_preferences">5. Optionally tweak the editor preferences</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Once things work, you may want to tweak them by going to editor <em>"Preferences"</em>. In particular:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The editor by default uses a bundled version of latest stable <em>FPC (Free Pascal Compiler)</em>. If you&#8217;d rather use your own <em>FPC</em> installation or <em>Delphi</em>, configure it in the preferences.</p>
</li>
<li>
<p>To edit the Pascal files, the editor by default tries to auto-detect various Pascal-capable  IDEs and editors, like <em>Lazarus</em>, <em>Delphi</em>, <em>Visual Studio Code</em>. If you prefer to configure a specific editor, choose it in the preferences.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>More details about the editor configuration can be found in our manual on <code><a href="https://castle-engine.io/install" class="bare">https://castle-engine.io/install</a></code> .</p>
</div>
<div class="paragraph">
<p>The editor can use any Pascal compiler and any text editor. We deliberately don&#8217;t put any special requirements on what you can use. Though we make sure to support the popular choices perfectly. In particular, we have a dedicated support for using <em>Visual Studio Code</em> with Pascal (and <em>Castle Game Engine</em> in particular), see <code><a href="https://castle-engine.io/vscode" class="bare">https://castle-engine.io/vscode</a></code> .</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_learning_to_design_3d_items_in_a_viewport">6. Learning to design 3D items in a viewport</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you haven&#8217;t already, open the <code>main</code> view in the editor.</p>
</div>
<div class="paragraph">
<p>You can double-click on it in the <em>"Open Existing View"</em> panel or in the <em>"Files"</em> panel (when you&#8217;re exploring the <code>data</code> subdirectory).</p>
</div>
<div class="paragraph">
<p>The initial view is mostly empty.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>It has a root component <code>Group1</code>, which is an instance of <code>TCastleUserInterface</code>. This component will contain everything else we design.</p>
</li>
<li>
<p>And it has a label <code>LabelFps</code> (an instance of <code>TCastleLabel</code> class). At run-time, this label will display the FPS counter.</p>
</li>
</ul>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/view_empty.png" alt="Initial view">
</div>
</div>
<div class="paragraph">
<p>Let&#8217;s add more content to it. First of all, to display anything in 3D, you need a <em>viewport</em>. A viewport is a way to display 3D or 2D content. It is an instance of <code>TCastleViewport</code> class. Add it to the design by right-clicking on the <code>Group1</code> component and choosing <em>"Add User Interface &#8594; Viewport (3D)"</em> from the menu that appears.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/add_viewport.png" alt="Adding a viewport">
</div>
</div>
<div class="paragraph">
<p>The result should look like this:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/view_viewport.png" alt="View with a viewport">
</div>
</div>
<div class="paragraph">
<p>Following this, drag the new <code>Viewport1</code> component above the <code>LabelFps</code> in the <em>Hierachy</em> panel (on the left). This way the FPS counter will be displayed in front of the viewport.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/view_viewport_fps_front.png" alt="FPS counter in front of the viewport">
</div>
</div>
<div class="paragraph">
<p>Now play around in the 3D view. There are 3 objects in 3D world:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Camera, called just <code>Camera1</code>, determines what the user will actually see once the game is run.</p>
</li>
<li>
<p>Light source makes things <em>lit</em> (bright). The initial light source is called <code>PointLight1</code> and it is an instance of <code>TCastlePointLight</code>, which is a simple light that shines in all directions from a given 3D position.</p>
</li>
<li>
<p>Rectangle representing a ground called a <code>Plane1</code>. Mathematically speaking, it&#8217;s not a <em>plane</em>, it&#8217;s a rectangle&#8201;&#8212;&#8201;however calling this a <em>"plane"</em> is a convention used by a lot of 3D software.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Click and hold the <em>right mouse button</em> over the viewport to look around. Use the AWSD keys to move. Use the <em>mouse scroll</em> (while holding the right mouse button pressed) to increase or decrease the movement speed.</p>
</div>
<div class="paragraph">
<p>Play around with <strong>moving the items</strong>. Drag the 3D axis to move any object.</p>
</div>
<div class="paragraph">
<p>Play around with <strong>adding new 3D items</strong>. Right-click on <code>Items</code> component inside the <code>Viewport1</code> and from the context menu add primitives like <em>"Box"</em>, <em>"Sphere"</em>, <em>"Cylinder"</em>. Move them around, delete them (with <em>Delete</em> key), duplicate (with <em>Ctrl+D</em> key).</p>
</div>
<div class="paragraph">
<p>Change some properties. On the right side, you can see an <em>object inspector</em>, familiar to any Lazarus and Delphi user. Adjust the properties, for example change the <code>Size</code> of the <code>Plane1</code> to be much bigger. Click on <em>"&#8230;&#8203;"</em> (3 dots) button at the <em>"Color"</em> property of any primitive (like a plane, a box, a sphere&#8230;&#8203;) to change the color.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/viewport_fun.png" alt="Example result of adding some 3D items and changing their properties">
</div>
</div>
<div class="paragraph">
<p>If you get stuck, consult our manual, in particular <code><a href="https://castle-engine.io/viewport_and_scenes" class="bare">https://castle-engine.io/viewport_and_scenes</a></code> and <code><a href="https://castle-engine.io/viewport_3d" class="bare">https://castle-engine.io/viewport_3d</a></code> are helpful to learn basic 3D manipulation.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_design_a_3d_chessboard_with_chess_pieces">7. Design a 3D chessboard with chess pieces</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Above we learned to design a 3D world composed from simple primitives, like boxes and spheres.</p>
</div>
<div class="paragraph">
<p>But this isn&#8217;t a way to create realistic 3D graphics. In most 3D graphic applications, the content is created using a specialized 3D <em>authoring tool</em>, like <em>Blender</em>. 3D artist creates a <em>mesh</em> (a set of vertexes, connected to form edges and polygons), assigns materials and textures, and exports the resulting object to a file that can be read by a game engine&#8201;&#8212;&#8201;like a glTF file.</p>
</div>
<div class="paragraph">
<p>glTF is a full-featured 3D model format developed by Khronos. <em>Castle Game Engine</em> has great support for glTF, see <code><a href="https://castle-engine.io/gltf" class="bare">https://castle-engine.io/gltf</a></code> for details.</p>
</div>
<div class="paragraph">
<p>On <em>Castle Game Engine</em> side, our most important component to display a 3D model is <code>TCastleScene</code>. It&#8217;s a big component, playing central role in our engine (in one way or another, it is actually responsible for all of 3D and 2D rendering in our viewport). Using it is simple: you create an instance of <code>TCastleScene</code> and set its <code>URL</code>  property to point to the model you want to display (like a glTF file). The <code>TCastleScene</code> class descends from the <code>TCastleTransform</code> class, and as such you can move, rotate and scale the <code>TCastleScene</code> instances. Alternatively, you can also drag-and-drop the glTF file from the <em>"Files"</em> panel to the viewport, editor will then automatically create a <code>TCastleScene</code> instance that loads the given model.</p>
</div>
<div class="paragraph">
<p>We support a number of 3D and 2D model formats, not only glTF. They are listed on <code><a href="https://castle-engine.io/creating_data_model_formats.php" class="bare">https://castle-engine.io/creating_data_model_formats.php</a></code> .</p>
</div>
<div class="paragraph">
<p>If you are capable of creating your own 3D models, for example in <em>Blender</em>, you can now make a detour: design a 3D model in Blender and export it to glTF using our instructions on <code><a href="https://castle-engine.io/blender" class="bare">https://castle-engine.io/blender</a></code> .</p>
</div>
<div class="paragraph">
<p>Or you can use some ready-made stuff:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>There&#8217;s a number of high-quality 3D content on the Internet, available also for free and on open-source-compatible licenses. We collect useful links on <code><a href="https://castle-engine.io/assets.php" class="bare">https://castle-engine.io/assets.php</a></code> .</p>
</li>
<li>
<p>Our engine also features an integration with <em>Sketchfab</em>, to allow you to search and download from a vast repository of free 3D models without leaving our editor. See the <code><a href="https://castle-engine.io/sketchfab" class="bare">https://castle-engine.io/sketchfab</a></code> documentation.</p>
<div class="paragraph">
<p>Here&#8217;s a sample&#8201;&#8212;&#8201;battle-hardened cat model, from Sketchfab, right inside our editor:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/cat_sketchfab.png" alt="Cat from Sketchfab">
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/sketchfab_cat_larger.png" alt="Cat from Sketchfab at runtime">
</div>
</div>
<div class="paragraph">
<p><em>Credits: The "Cat" 3D model was done by Muru (<code><a href="https://sketchfab.com/muru" class="bare">https://sketchfab.com/muru</a></code>) and is available on Sketchfab (<code><a href="https://sketchfab.com/3d-models/cat-16c3444c8d1440fc97fdf10f60ec58b0" class="bare">https://sketchfab.com/3d-models/cat-16c3444c8d1440fc97fdf10f60ec58b0</a></code>) on CC-BY-4.0 license</em>.</p>
</div>
</li>
<li>
<p>Finally, we have a ready set of 3D models for the chessboard and all chess pieces, that you can use for this demo.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>To use the last option, download the 3D models from <code><a href="https://github.com/castle-engine/bad-chess/releases/download/chess-models/chess-models.zip" class="bare">https://github.com/castle-engine/bad-chess/releases/download/chess-models/chess-models.zip</a></code> . They were made based on open-source Blender model published on <code><a href="https://blendswap.com/blend/29244" class="bare">https://blendswap.com/blend/29244</a></code> by <em>Phuong2647</em>.</p>
</div>
<div class="paragraph">
<p>Unpack the resulting archive anywhere <em>under the <code>data</code> subdirectory of your project</em>.</p>
</div>
<div class="paragraph">
<p>Then simply drag-and-drop the <code>*.gltf</code> files onto the viewport. Move and duplicate them as needed, to arrange them into a starting chess position.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
For our silly physics game, it actually completely doesn&#8217;t matter how you will arrange them. You also don&#8217;t need to position and rotate them perfectly. Have fun :)
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>This is an example result:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/view_chess.png" alt="Chessboard with chess pieces designed in the editor">
</div>
</div>
<div class="paragraph">
<p>Once you&#8217;ve designed the chessboard and put chess pieces on it, also make sure to adjust the lights to make everything nicely bright (but not too bright).</p>
</div>
<div class="paragraph">
<p>Finally, adjust the camera so that user sees a nice view of the board when the application starts. When you select a camera component (like <code>Camera1</code>, if you haven&#8217;t renamed the default camera), the editor shows a small window with <em>camera preview</em>. You can click <em>"Pin"</em> in this window to keep observing the world from this camera. There are basically 2 ways to manipulate the camera:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Move and rotate the camera just like any other 3D object. Look at the <em>camera preview</em> to judge whether the camera view looks good.</p>
</li>
<li>
<p>Or, alternatively, navigate in the editor and then use the menu item <em>"Viewport &#8594; Align Camera To View"</em> (key shortcut <em>Ctrl + Numpad 0</em>) to make the camera view match the current view in the editor.</p>
</li>
</ol>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/camera.png" alt="Camera adjustment">
</div>
</div>
<div class="paragraph">
<p>Once you have a nice view, make sure it all works: compile and run the application again.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/camera_game_running.png" alt="Camera adjustment">
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_using_physics_in_the_editor">8. Using physics in the editor</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Now that the proper chessboard with chess pieces is designed, let&#8217;s use physics to make things <em>crazier</em>.</p>
</div>
<div class="paragraph">
<p><em>Castle Game Engine</em> has a support for <em>rigid body physics</em>. This means that:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Objects can be affected by <em>forces</em>.</p>
<div class="paragraph">
<p>The force that works automatically is <em>gravity</em>, pulling objects down (in the direction of the negative Y axis, by default).</p>
</div>
<div class="paragraph">
<p>You can also define additional forces from code, to e.g. push things along an arbitary direction. Your own forces can realize a range of real-life effects, like wind, explosions, spinning tornadoes, etc.</p>
</div>
</li>
<li>
<p>Collisions between objects are automatically detected and resolved. That is, by default the objects will <em>bounce off</em> each other.</p>
<div class="paragraph">
<p>It is also possible to detect collisions in code and react to them in any way (e.g. an enemy may explode when it collides with a rocket).</p>
</div>
</li>
</ul>
</div>
<div class="ulist">
<ul>
<li>
<p>You can also connect certain objects using <em>joints</em>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>We will not explore all these features in our article, but we will show you how to enjoy the basics. To learn more about the possibilities, read our manual <code><a href="https://castle-engine.io/physics" class="bare">https://castle-engine.io/physics</a></code> and play with demoes in the <code>examples/physics/</code> subdirectory of the engine. Here&#8217;s a screenshot from one of the demos, showing explicit application of physics forces:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/physics_forces.png" alt="Physics Forces">
</div>
</div>
<div class="paragraph">
<p><em>Castle Game Engine</em> physics internally uses <em>Kraft</em>, a physics engine deveveloped in Pascal by <em>Benjamin 'BeRo' Rosseaux</em>.</p>
</div>
<div class="paragraph">
<p>Any component descending <code>TCastleTransform</code>, including primitives (like <code>TCastleBox</code>) or scenes loaded from models (<code>TCastleScene</code>) or a group of other objects (<code>TCastleTransform</code> with children) can be a <em>rigid body</em> for the physics engine that participates in the collision detection and resulting movement. The object needs to have two <em>behaviors</em>:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>TCastleRigidBody</code> behavior makes the component a <em>rigid body</em>. It defines common physics properties, like whether the object is affected by gravity and the initial movement speed.</p>
</li>
<li>
<p>A <em>collider</em>, which stands for <em>any component descending from the abstract class <code>TCastleCollider</code></em>. Many collider shapes are possible, like <code>TCastleSphereCollider</code>, <code>TCastleBoxCollider</code> and <code>TCastleMeshCollider</code>.</p>
<div class="paragraph">
<p>Using the <code>TCastleMeshCollider</code> results in most precise collisions, but the colliding object must be <em>static</em> which means that other objects will <em>bounce off</em> this object, but the object with <code>TCastleMeshCollider</code> will not move itself.</p>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>The term <em>behavior</em> we used above is a special mechanism in <em>Castle Game Engine</em> to attach additional functionality to a <code>TCastleTransform</code>. Behaviors are a great way to define various functionality that enhances given game object. There are various built-in behaviors and you can also define your own. See <code><a href="https://castle-engine.io/behaviors" class="bare">https://castle-engine.io/behaviors</a></code> for more information.</p>
</div>
<div class="paragraph">
<p>After this overview, you&#8217;re ready to actually use physics in our chess game.</p>
</div>
<div class="paragraph">
<p>Right-click on the component representing the chessboard. From the context menu choose <em>"Add Behavior (Extends Parent Transform) &#8594; Physics &#8594; Collider &#8594; Mesh"</em>. In response, you will notice that 2 components have appeared in the component tree: <code>MeshCollider1</code> and <code>RigidBody1</code>. That&#8217;s a convenience feature of the editor: adding a collider also adds a rigid body component.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/physics_chessboard.png" alt="Chessboard with rigid body and colliders">
</div>
</div>
<div class="paragraph">
<p>Next choose any chess piece. Right-click on it and from the context menu choose <em>"Add Behavior (Extends Parent Transform) &#8594; Physics &#8594; Collider &#8594; Box"</em>. Note that we use a simpler collider for the chess piece, which is also dynamic. This will allow the chess piece to actually fall down on the board.</p>
</div>
<div class="paragraph">
<p>Finally move the chess piece to a more dramatic position, above the board, so that it will fall down when the physics will start.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/physics_above.png" alt="Chess piece (white pawn) with physics above the board">
</div>
</div>
<div class="paragraph">
<p>We are ready to run physics. One way would be to just run the application, using the <em>"Compile And Run"</em> as you&#8217;ve done before. But there&#8217;s a quicker way to experiment with physics: run <em>physics simulation</em> by using the green play icon at the header of the editor (or menu item <em>"Physics &#8594; Play Simulation"</em>, key shortcut <em>Ctrl+P</em>).</p>
</div>
<div class="paragraph">
<p>Do this and watch in awe as the pawn falls on the board.</p>
</div>
<div class="paragraph">
<p>Remember to finish the physics simulation when you&#8217;re done (press the green stop button, or again menu item <em>"Physics &#8594; Play Simulation"</em>, key shortcut <em>Ctrl+P</em>). Editing the design during the physics simulation is allowed (and it&#8217;s a great way to experiment with various physics settings) but <em>the changes are not saved when physics simulation is running</em>. That&#8217;s because physics typically moves the objects, and you don&#8217;t want to save this position resulting from physics interactions. So be sure to <em>stop the physics simulation before doing any persistent changes to the design</em>.</p>
</div>
<div class="paragraph">
<p>To get more spectacular results:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Add physics colliders to more chess pieces.</p>
</li>
<li>
<p>Move the chess pieces to more interesting positions, so that multiple pieces will fall down from above on multiple other chess pieces.</p>
</li>
<li>
<p>You can also duplicate (key shortcut <em>Ctrl+D</em>) the chess pieces (it will duplicate the whole selected object, including physics behaviors if any). That
s an easy way to have a lot of physical objects that bounce off each other.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>After each change, just play and stop physics simulation again.</p>
</div>
<div class="paragraph">
<p>Make sure that the initial position of all rigid bodies does not make some pair collide with each other right at the start. If the two objects will collide at start, physics engine may (sometimes quite explosively) move them away from each other.</p>
</div>
<div class="paragraph">
<p>This is a sample result:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/physics_chess_more_dramatic.png" alt="Chess pieces after a more dramatic physics simulation">
</div>
</div>
<div class="paragraph">
<p>One last thing remains to learn in this (first) part of the article: how to <em>flick</em> the chess piece?</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>From Pascal code you can use various methods to apply a force on a rigid body. More about this in the next article part. You can also experiment with the example application <code>examples/physics/physics_forces/</code> if you&#8217;re impatient.</p>
</li>
<li>
<p>Or you can set a specific <code>LinearVelocity</code> on a rigid body component.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>We will use the latter approach, as it can be trivially done and tested in the editor.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Select the chess piece. Any chess piece you want to <em>"flick"</em> (throw across the board).</p>
</li>
<li>
<p>Make sure it has a collider and rigid body components (if not, add them, as above).</p>
</li>
<li>
<p>Select the <code>TCastleRigidBody</code> component of it, and find the <code>LinearVelocity</code> property in it.</p>
</li>
<li>
<p>Set <code>LinearVelocity</code> to any large non-zero vector, like <code>-100 0 0</code>. This means we have a velocity of 100 units per second in the negative X direction.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Run the physics simulation and watch the mayhem.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="images_1/physics_flick.png" alt="Flicked chess piece">
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_summary">9. Summary</h2>
<div class="sectionbody">
<div class="paragraph">
<p>We have designed a 3D application using <em>Castle Game Engine</em> with a bit of physics. We didn&#8217;t yet write any Pascal code to do any interactions&#8201;&#8212;&#8201;this will be done in the next part of the article.</p>
</div>
<div class="paragraph">
<p>If you want to download a ready application, resulting from this, go to <code><a href="https://github.com/castle-engine/bad-chess" class="bare">https://github.com/castle-engine/bad-chess</a></code> . The subdirectory <code>project</code> of that repository contains the final working demo of this. It will be extended in the next part of the article.</p>
</div>
<div class="paragraph">
<p>I hope you had fun doing this demo and exploring the possibilities of <em>Castle Game Engine</em>.</p>
</div>
<div class="paragraph">
<p>If you have any questions or feedback about the engine, don&#8217;t be shy! Speak up, ask and share your comments on our forum <code><a href="https://forum.castle-engine.io" class="bare">https://forum.castle-engine.io</a></code> or Discord <code><a href="https://castle-engine.io/talk.php" class="bare">https://castle-engine.io/talk.php</a></code> .</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2024-09-16 18:38:03 +0200
</div>
</div>
</body>
</html>